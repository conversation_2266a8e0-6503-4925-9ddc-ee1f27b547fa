!function(n,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((n="undefined"!=typeof globalThis?globalThis:n||self).jotaiVanillaInternals={})}(this,function(n){"use strict";function e(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=Array(e);t<e;t++)r[t]=n[t];return r}function t(n,t){var r="undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(r)return(r=r.call(n)).next.bind(r);if(Array.isArray(n)||(r=function(n,t){if(n){if("string"==typeof n)return e(n,t);var r={}.toString.call(n).slice(8,-1);return"Object"===r&&n.constructor&&(r=n.constructor.name),"Map"===r||"Set"===r?Array.from(n):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?e(n,t):void 0}}(n))||t){r&&(n=r);var o=0;return function(){return o>=n.length?{done:!0}:{done:!1,value:n[o++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var r=function(n,e){return n.unstable_is?n.unstable_is(e):e===n},o=function(n){return"init"in n},a=function(n){return!!n.write},i=function(n){return"v"in n||"e"in n},u=function(n){if("e"in n)throw n.e;return n.v},l=new WeakMap,f=function(n){var e;return c(n)&&!(null==(e=l.get(n))||!e[0])},d=function(n){var e=l.get(n);null!=e&&e[0]&&(e[0]=!1,e[1].forEach(function(n){return n()}))},v=function(n,e){var t=l.get(n);if(!t){t=[!0,new Set],l.set(n,t);var r=function(){t[0]=!1};n.then(r,r)}t[1].add(e)},c=function(n){return"function"==typeof(null==n?void 0:n.then)},s=function(n,e,t){t.p.has(n)||(t.p.add(n),e.then(function(){t.p.delete(n)},function(){t.p.delete(n)}))},h=function(n,e,r){var o=r(n),a="v"in o,i=o.v;if(c(e))for(var u,l=t(o.d.keys());!(u=l()).done;){var f=u.value;s(n,e,r(f))}o.v=e,delete o.e,a&&Object.is(i,o.v)||(++o.n,c(i)&&d(i))},y=function(n,e,r){for(var o,a=new Set,i=t((null==(u=r.get(n))?void 0:u.t)||[]);!(o=i()).done;){var u,l=o.value;r.has(l)&&a.add(l)}for(var f,d=t(e.p);!(f=d()).done;){var v=f.value;a.add(v)}return a},g=function(){var n={},e=new WeakMap,t=function(t){var r,o;null==(r=e.get(n))||r.forEach(function(n){return n(t)}),null==(o=e.get(t))||o.forEach(function(n){return n()})};return t.add=function(t,r){var o=t||n,a=(e.has(o)?e:e.set(o,new Set)).get(o);return a.add(r),function(){null==a||a.delete(r),a.size||e.delete(o)}},t},p=Symbol(),w=function(n,e,l,d,g,w,A,m,N,b,E){void 0===n&&(n=new WeakMap),void 0===e&&(e=new WeakMap),void 0===l&&(l=new WeakMap),void 0===d&&(d=new Set),void 0===g&&(g=new Set),void 0===w&&(w=new Set),void 0===A&&(A={}),void 0===m&&(m=function(n){for(var e=arguments.length,t=new Array(e>1?e-1:0),r=1;r<e;r++)t[r-1]=arguments[r];return n.read.apply(n,t)}),void 0===N&&(N=function(n){for(var e=arguments.length,t=new Array(e>1?e-1:0),r=1;r<e;r++)t[r-1]=arguments[r];return n.write.apply(n,t)}),void 0===b&&(b=function(n,e){return null==n.unstable_onInit?void 0:n.unstable_onInit(e)}),void 0===E&&(E=function(n,e){return null==n.onMount?void 0:n.onMount(e)});var S=(arguments.length<=11?void 0:arguments[11])||function(e){var t=n.get(e);return t||(t={d:new Map,p:new Set,n:0},n.set(e,t),null==b||b(e,W)),t},I=(arguments.length<=12?void 0:arguments[12])||function(){var n=[],t=function(e){try{e()}catch(e){n.push(e)}},r=function(){A.f&&t(A.f);var n=new Set,r=n.add.bind(n);d.forEach(function(n){var t;return null==(t=e.get(n))?void 0:t.l.forEach(r)}),d.clear(),w.forEach(r),w.clear(),g.forEach(r),g.clear(),n.forEach(t),d.size&&T()};do{r()}while(d.size||w.size||g.size);if(n.length)throw new AggregateError(n)},T=(arguments.length<=13?void 0:arguments[13])||function(){for(var n=[],r=new WeakSet,o=new WeakSet,a=Array.from(d);a.length;){var i=a[a.length-1],u=S(i);if(o.has(i))a.pop();else if(r.has(i))l.get(i)===u.n&&n.push([i,u]),o.add(i),a.pop();else{r.add(i);for(var f,v=t(y(i,u,e));!(f=v()).done;){var c=f.value;r.has(c)||a.push(c)}}}for(var s=n.length-1;s>=0;--s){for(var h,g=n[s],p=g[0],w=!1,A=t(g[1].d.keys());!(h=A()).done;){var m=h.value;if(m!==p&&d.has(m)){w=!0;break}}w&&(_(p),k(p)),l.delete(p)}},_=(arguments.length<=14?void 0:arguments[14])||function(n){var t=S(n);if(i(t)){if(e.has(n)&&l.get(n)!==t.n)return t;if(Array.from(t.d).every(function(n){var e=n[0],t=n[1];return _(e).n===t}))return t}t.d.clear();var y,g,p=!0,w=function(){e.has(n)&&(k(n),T(),I())},N={get signal(){return y||(y=new AbortController),y.signal},get setSelf(){return!g&&a(n)&&(g=function(){if(!p)try{for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return L.apply(void 0,[n].concat(t))}finally{T(),I()}}),g}},b=t.n;try{var E=m(n,function(a){if(r(n,a)){var l=S(a);if(!i(l)){if(!o(a))throw new Error("no atom init");h(a,a.init,S)}return u(l)}var d=_(a);try{return u(d)}finally{var v;t.d.set(a,d.n),f(t.v)&&s(n,t.v,d),null==(v=e.get(a))||v.t.add(n),p||w()}},N);return h(n,E,S),c(E)&&(v(E,function(){var n;return null==(n=y)?void 0:n.abort()}),E.then(w,w)),t}catch(n){return delete t.v,t.e=n,++t.n,t}finally{p=!1,b!==t.n&&l.get(n)===b&&(l.set(n,t.n),d.add(n),null==A.c||A.c(n))}},R=(arguments.length<=15?void 0:arguments[15])||function(n){for(var r=[n];r.length;)for(var o,a=r.pop(),i=S(a),u=t(y(a,i,e));!(o=u()).done;){var f=o.value,d=S(f);l.set(f,d.n),r.push(f)}},L=(arguments.length<=16?void 0:arguments[16])||function(n){var e=!0;try{for(var t=arguments.length,a=new Array(t>1?t-1:0),i=1;i<t;i++)a[i-1]=arguments[i];return N.apply(void 0,[n,function(n){return u(_(n))},function(t){var a=S(t);try{for(var i=arguments.length,u=new Array(i>1?i-1:0),l=1;l<i;l++)u[l-1]=arguments[l];if(r(n,t)){if(!o(t))throw new Error("atom not writable");var f=a.n,v=u[0];return h(t,v,S),k(t),void(f!==a.n&&(d.add(t),null==A.c||A.c(t),R(t)))}return L.apply(void 0,[t].concat(u))}finally{e||(T(),I())}}].concat(a))}finally{e=!1}},k=(arguments.length<=17?void 0:arguments[17])||function(n){var r=S(n),o=e.get(n);if(o&&!f(r.v)){for(var a,i=t(r.d);!(a=i()).done;){var u=a.value,l=u[0],v=u[1];if(!o.d.has(l)){var c=S(l);M(l).t.add(n),o.d.add(l),v!==c.n&&(d.add(l),null==A.c||A.c(l),R(l))}}for(var s,h=t(o.d||[]);!(s=h()).done;){var y=s.value;if(!r.d.has(y)){o.d.delete(y);var g=P(y);null==g||g.t.delete(n)}}}},M=(arguments.length<=18?void 0:arguments[18])||function(n){var r=S(n),o=e.get(n);if(!o){_(n);for(var i,u=t(r.d.keys());!(i=u()).done;){var l=i.value;M(l).t.add(n)}if(o={l:new Set,d:new Set(r.d.keys()),t:new Set},e.set(n,o),null==A.m||A.m(n),a(n)){g.add(function(){var e=!0;try{var t=E(n,function(){try{for(var t=arguments.length,r=new Array(t),o=0;o<t;o++)r[o]=arguments[o];return L.apply(void 0,[n].concat(r))}finally{e||(T(),I())}});t&&(o.u=function(){e=!0;try{t()}finally{e=!1}})}finally{e=!1}})}}return o},P=(arguments.length<=19?void 0:arguments[19])||function(n){var r=S(n),o=e.get(n);if(!o||o.l.size||Array.from(o.t).some(function(t){var r;return null==(r=e.get(t))?void 0:r.d.has(n)}))return o;o.u&&w.add(o.u),o=void 0,e.delete(n),null==A.u||A.u(n);for(var a,i=t(r.d.keys());!(a=i()).done;){var u=a.value,l=P(u);null==l||l.t.delete(n)}},z=[n,e,l,d,g,w,A,m,N,b,E,S,I,T,_,R,L,k,M,P],W={get:function(n){return u(_(n))},set:function(n){try{for(var e=arguments.length,t=new Array(e>1?e-1:0),r=1;r<e;r++)t[r-1]=arguments[r];return L.apply(void 0,[n].concat(t))}finally{T(),I()}},sub:function(n,e){var t=M(n).l;return t.add(e),I(),function(){t.delete(e),P(n),I()}}};return Object.defineProperty(W,p,{value:z}),W},A=function(n){return n[p]},m=function(n){var e,t,r,o,a,i;return(e=n).c||(e.c=g()),(t=n).m||(t.m=g()),(r=n).u||(r.u=g()),(o=n).f||(o.f=(a=new Set,(i=function(){a.forEach(function(n){return n()})}).add=function(n){return a.add(n),function(){a.delete(n)}},i)),n},N=r,b=o,E=a,S=i,I=u,T=l,_=f,R=d,L=v,k=c,M=s,P=h,z=y;n.INTERNAL_abortPromise=R,n.INTERNAL_addPendingPromiseToDependency=M,n.INTERNAL_buildStoreRev1=w,n.INTERNAL_getBuildingBlocksRev1=A,n.INTERNAL_getMountedOrPendingDependents=z,n.INTERNAL_hasInitialValue=b,n.INTERNAL_initializeStoreHooks=m,n.INTERNAL_isActuallyWritableAtom=E,n.INTERNAL_isAtomStateInitialized=S,n.INTERNAL_isPendingPromise=_,n.INTERNAL_isPromiseLike=k,n.INTERNAL_isSelfAtom=N,n.INTERNAL_promiseStateMap=T,n.INTERNAL_registerAbortHandler=L,n.INTERNAL_returnAtomValue=I,n.INTERNAL_setAtomStateValueOrPromise=P});
