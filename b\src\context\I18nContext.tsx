/**
 * 国际化上下文
 */

import { createContext, useContext, JSX, createSignal } from 'solid-js';
import messages, { Language } from '../i18n';

interface I18nContextType {
  language: () => Language;
  setLanguage: (lang: Language) => void;
  t: (key: string, params?: Record<string, string | number>) => string;
  availableLanguages: Language[];
}

const I18nContext = createContext<I18nContextType>();

interface I18nProviderProps {
  children: JSX.Element;
  defaultLanguage?: Language;
}

export function I18nProvider(props: I18nProviderProps) {
  // 从本地存储获取语言设置，或使用默认语言
  const getInitialLanguage = (): Language => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('language') as Language;
      if (saved && ['zh-CN', 'en-US'].includes(saved)) {
        return saved;
      }
      // 检测浏览器语言
      const browserLang = navigator.language;
      if (browserLang.startsWith('zh')) {
        return 'zh-CN';
      }
    }
    return props.defaultLanguage || 'zh-CN';
  };

  const [language, setLanguageSignal] = createSignal<Language>(getInitialLanguage());

  const setLanguage = (lang: Language) => {
    setLanguageSignal(lang);
    if (typeof window !== 'undefined') {
      localStorage.setItem('language', lang);
    }
  };

  // 翻译函数
  const t = (key: string, params?: Record<string, string | number>): string => {
    const currentLang = language();
    const langMessages = messages[currentLang];
    
    // 支持嵌套键，如 'auth.login'
    const keys = key.split('.');
    let value: any = langMessages;
    
    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k];
      } else {
        // 如果找不到翻译，返回键名
        console.warn(`Translation key not found: ${key} for language: ${currentLang}`);
        return key;
      }
    }
    
    if (typeof value !== 'string') {
      console.warn(`Translation value is not a string: ${key}`);
      return key;
    }
    
    // 参数替换
    if (params) {
      return value.replace(/\{\{(\w+)\}\}/g, (match, paramKey) => {
        return params[paramKey]?.toString() || match;
      });
    }
    
    return value;
  };

  const availableLanguages: Language[] = ['zh-CN', 'en-US'];

  const contextValue: I18nContextType = {
    language,
    setLanguage,
    t,
    availableLanguages
  };

  return (
    <I18nContext.Provider value={contextValue}>
      {props.children}
    </I18nContext.Provider>
  );
}

export function useI18n(): I18nContextType {
  const context = useContext(I18nContext);
  if (!context) {
    throw new Error('useI18n must be used within an I18nProvider');
  }
  return context;
}

// 便捷的翻译 hook
export function useTranslation() {
  const { t, language, setLanguage } = useI18n();
  return { t, language, setLanguage };
}

// 语言切换组件
export function LanguageSwitcher() {
  const { language, setLanguage, availableLanguages } = useI18n();

  return (
    <select
      value={language()}
      onChange={(e) => setLanguage(e.target.value as Language)}
      style={{
        padding: '4px 8px',
        'border-radius': '4px',
        border: '1px solid #d1d5db',
        'background-color': 'white',
        'font-size': '14px'
      }}
    >
      {availableLanguages.map(lang => (
        <option value={lang}>
          {lang === 'zh-CN' ? '中文' : 'English'}
        </option>
      ))}
    </select>
  );
}
