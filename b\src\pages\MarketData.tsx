import { createSignal, For } from 'solid-js'
import { css } from '../../styled-system/css'

export default function MarketData() {
  const [selectedSymbol, setSelectedSymbol] = createSignal('000001')
  const [timeframe, setTimeframe] = createSignal('1d')

  // 热门股票数据
  const stockList = [
    { symbol: '000001', name: '平安银行', price: 12.34, change: 0.12, changePercent: 0.98, volume: '1.2亿' },
    { symbol: '000002', name: '万科A', price: 18.56, change: -0.23, changePercent: -1.22, volume: '8900万' },
    { symbol: '000858', name: '五粮液', price: 156.78, change: 2.34, changePercent: 1.52, volume: '5600万' },
    { symbol: '600036', name: '招商银行', price: 45.67, change: 0.89, changePercent: 1.99, volume: '2.1亿' },
    { symbol: '600519', name: '贵州茅台', price: 1678.90, change: -12.34, changePercent: -0.73, volume: '3400万' },
    { symbol: '000858', name: '腾讯控股', price: 345.60, change: 8.90, changePercent: 2.64, volume: '1.8亿' },
    { symbol: '600036', name: '比亚迪', price: 234.50, change: -5.60, changePercent: -2.33, volume: '1.5亿' },
    { symbol: '600519', name: '宁德时代', price: 456.78, change: 12.30, changePercent: 2.77, volume: '2.3亿' }
  ]

  // 市场指数数据
  const indexData = [
    { name: '上证指数', value: '3,245.67', change: '+23.45', percent: '+0.73%', trend: 'up' },
    { name: '深证成指', value: '12,156.89', change: '-45.67', percent: '-0.37%', trend: 'down' },
    { name: '创业板指', value: '2,456.78', change: '+67.89', percent: '+2.84%', trend: 'up' },
    { name: '科创50', value: '1,234.56', change: '+34.56', percent: '+2.88%', trend: 'up' }
  ]

  // 行业板块数据
  const sectorData = [
    { name: '新能源汽车', change: '+3.45%', trend: 'up', stocks: 156 },
    { name: '人工智能', change: '+2.78%', trend: 'up', stocks: 89 },
    { name: '生物医药', change: '-1.23%', trend: 'down', stocks: 234 },
    { name: '半导体', change: '+4.56%', trend: 'up', stocks: 67 },
    { name: '新材料', change: '+1.89%', trend: 'up', stocks: 123 },
    { name: '军工', change: '-0.67%', trend: 'down', stocks: 45 }
  ]

  return (
    <div class={css({
      display: 'flex',
      flexDirection: 'column',
      gap: '24px'
    })}>
      {/* 市场指数概览 */}
      <div class={css({
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
        gap: '16px'
      })}>
        <For each={indexData}>
          {(index) => (
            <div class={css({
              bg: 'white',
              borderRadius: '12px',
              p: '20px',
              boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
              border: '1px solid #f0f0f0',
              transition: 'all 0.3s ease',
              _hover: {
                boxShadow: '0 4px 16px rgba(0,0,0,0.15)',
                transform: 'translateY(-2px)'
              }
            })}>
              <div class={css({
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                mb: '12px'
              })}>
                <span class={css({
                  fontSize: '14px',
                  color: '#666',
                  fontWeight: '500'
                })}>
                  {index.name}
                </span>
                <div class={css({
                  width: '8px',
                  height: '8px',
                  borderRadius: '50%',
                  bg: index.trend === 'up' ? '#52c41a' : '#ff4d4f'
                })} />
              </div>

              <div class={css({
                fontSize: '24px',
                fontWeight: '700',
                color: '#333',
                mb: '8px'
              })}>
                {index.value}
              </div>

              <div class={css({
                display: 'flex',
                alignItems: 'center',
                gap: '8px'
              })}>
                <span class={css({
                  fontSize: '14px',
                  color: index.trend === 'up' ? '#52c41a' : '#ff4d4f',
                  fontWeight: '500'
                })}>
                  {index.change}
                </span>
                <span class={css({
                  fontSize: '12px',
                  color: index.trend === 'up' ? '#52c41a' : '#ff4d4f',
                  bg: index.trend === 'up' ? '#f6ffed' : '#fff2f0',
                  px: '6px',
                  py: '2px',
                  borderRadius: '4px'
                })}>
                  {index.percent}
                </span>
              </div>
            </div>
          )}
        </For>
      </div>

      {/* 热门股票列表 */}
      <div class={css({
        bg: 'white',
        borderRadius: '16px',
        p: '24px',
        boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
        border: '1px solid #f0f0f0'
      })}>
        <div class={css({
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          mb: '20px'
        })}>
          <h3 class={css({
            fontSize: '18px',
            fontWeight: '600',
            color: '#262626',
            margin: 0
          })}>
            热门股票
          </h3>
          <div class={css({
            display: 'flex',
            gap: '8px'
          })}>
            <For each={['1d', '5d', '1m', '3m']}>
              {(tf) => (
                <button
                  type="button"
                  class={css({
                    px: '12px',
                    py: '6px',
                    border: '1px solid #d9d9d9',
                    borderRadius: '6px',
                    bg: timeframe() === tf ? '#1890ff' : 'white',
                    color: timeframe() === tf ? 'white' : '#333',
                    fontSize: '12px',
                    cursor: 'pointer',
                    transition: 'all 0.2s ease',
                    _hover: {
                      bg: timeframe() === tf ? '#40a9ff' : '#f5f5f5'
                    }
                  })}
                  onClick={() => setTimeframe(tf)}
                >
                  {tf}
                </button>
              )}
            </For>
          </div>
        </div>

        <div class={css({
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
          gap: '12px'
        })}>
          <For each={stockList}>
            {(stock) => (
              <div
                class={css({
                  p: '16px',
                  borderRadius: '8px',
                  bg: selectedSymbol() === stock.symbol ? '#e6f7ff' : '#fafafa',
                  border: selectedSymbol() === stock.symbol ? '1px solid #1890ff' : '1px solid transparent',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease',
                  _hover: { bg: '#f0f0f0' }
                })}
                onClick={() => setSelectedSymbol(stock.symbol)}
              >
                <div class={css({
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  mb: '8px'
                })}>
                  <div>
                    <div class={css({
                      fontSize: '14px',
                      fontWeight: '600',
                      color: '#262626',
                      mb: '2px'
                    })}>
                      {stock.name}
                    </div>
                    <div class={css({
                      fontSize: '12px',
                      color: '#8c8c8c'
                    })}>
                      {stock.symbol}
                    </div>
                  </div>
                  <div class={css({
                    textAlign: 'right'
                  })}>
                    <div class={css({
                      fontSize: '16px',
                      fontWeight: '700',
                      color: '#262626',
                      mb: '2px'
                    })}>
                      ¥{stock.price.toFixed(2)}
                    </div>
                    <div class={css({
                      display: 'flex',
                      alignItems: 'center',
                      gap: '4px',
                      justifyContent: 'flex-end'
                    })}>
                      <span class={css({
                        fontSize: '12px',
                        color: stock.change >= 0 ? '#52c41a' : '#ff4d4f',
                        fontWeight: '500'
                      })}>
                        {stock.change >= 0 ? '+' : ''}{stock.change.toFixed(2)}
                      </span>
                      <span class={css({
                        fontSize: '11px',
                        color: stock.changePercent >= 0 ? '#52c41a' : '#ff4d4f',
                        bg: stock.changePercent >= 0 ? '#f6ffed' : '#fff2f0',
                        px: '4px',
                        py: '1px',
                        borderRadius: '3px'
                      })}>
                        {stock.changePercent >= 0 ? '+' : ''}{stock.changePercent.toFixed(2)}%
                      </span>
                    </div>
                  </div>
                </div>
                <div class={css({
                  fontSize: '11px',
                  color: '#8c8c8c'
                })}>
                  成交量: {stock.volume}
                </div>
              </div>
            )}
          </For>
        </div>
      </div>

      {/* 行业板块 */}
      <div class={css({
        bg: 'white',
        borderRadius: '16px',
        p: '24px',
        boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
        border: '1px solid #f0f0f0'
      })}>
        <h3 class={css({
          fontSize: '18px',
          fontWeight: '600',
          color: '#262626',
          margin: 0,
          mb: '20px'
        })}>
          行业板块
        </h3>

        <div class={css({
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '12px'
        })}>
          <For each={sectorData}>
            {(sector) => (
              <div class={css({
                p: '16px',
                borderRadius: '8px',
                bg: '#fafafa',
                border: '1px solid #f0f0f0',
                transition: 'all 0.2s ease',
                cursor: 'pointer',
                _hover: { bg: '#f0f0f0' }
              })}>
                <div class={css({
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  mb: '8px'
                })}>
                  <span class={css({
                    fontSize: '14px',
                    fontWeight: '500',
                    color: '#262626'
                  })}>
                    {sector.name}
                  </span>
                  <span class={css({
                    fontSize: '12px',
                    color: sector.trend === 'up' ? '#52c41a' : '#ff4d4f',
                    bg: sector.trend === 'up' ? '#f6ffed' : '#fff2f0',
                    px: '6px',
                    py: '2px',
                    borderRadius: '4px',
                    fontWeight: '500'
                  })}>
                    {sector.change}
                  </span>
                </div>
                <div class={css({
                  fontSize: '12px',
                  color: '#8c8c8c'
                })}>
                  {sector.stocks} 只股票
                </div>
              </div>
            )}
          </For>
        </div>
      </div>
    </div>
  )
}


