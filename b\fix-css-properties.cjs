const fs = require('fs');
const path = require('path');

// CSS属性映射表
const cssPropertyMap = {
  'backgroundColor': 'background-color',
  'borderRadius': 'border-radius',
  'fontSize': 'font-size',
  'fontWeight': 'font-weight',
  'textAlign': 'text-align',
  'marginTop': 'margin-top',
  'marginBottom': 'margin-bottom',
  'marginLeft': 'margin-left',
  'marginRight': 'margin-right',
  'paddingTop': 'padding-top',
  'paddingBottom': 'padding-bottom',
  'paddingLeft': 'padding-left',
  'paddingRight': 'padding-right',
  'borderBottom': 'border-bottom',
  'borderTop': 'border-top',
  'borderLeft': 'border-left',
  'borderRight': 'border-right',
  'overflowY': 'overflow-y',
  'overflowX': 'overflow-x',
  'textDecoration': 'text-decoration',
  'flexDirection': 'flex-direction',
  'alignItems': 'align-items',
  'justifyContent': 'justify-content',
  'flexWrap': 'flex-wrap',
  'gridTemplateColumns': 'grid-template-columns',
  'gridGap': 'grid-gap',
  'lineHeight': 'line-height',
  'boxShadow': 'box-shadow',
  'borderColor': 'border-color',
  'borderStyle': 'border-style',
  'borderWidth': 'border-width',
  'maxWidth': 'max-width',
  'minWidth': 'min-width',
  'maxHeight': 'max-height',
  'minHeight': 'min-height',
  'whiteSpace': 'white-space',
  'wordBreak': 'word-break',
  'alignSelf': 'align-self',
  'justifySelf': 'justify-self',
  'pointerEvents': 'pointer-events',
  'userSelect': 'user-select',
  'objectFit': 'object-fit',
  'textTransform': 'text-transform',
  'letterSpacing': 'letter-spacing',
  'zIndex': 'z-index'
};

function fixCssPropertiesInFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;

  // 处理style对象中的属性 - 支持多行
  // 匹配 style={{ ... }} 包括多行情况
  const styleRegex = /style\s*=\s*\{\{([^}]*(?:\{[^}]*\}[^}]*)*)\}\}/gs;

  content = content.replace(styleRegex, (match, styleContent) => {
    let newStyleContent = styleContent;

    for (const [camelCase, kebabCase] of Object.entries(cssPropertyMap)) {
      // 匹配属性名，支持各种格式
      const patterns = [
        new RegExp(`(\\s|^)${camelCase}(\\s*:)`, 'g'),
        new RegExp(`(['"])${camelCase}(['"]\\s*:)`, 'g')
      ];

      patterns.forEach(regex => {
        if (regex.test(newStyleContent)) {
          newStyleContent = newStyleContent.replace(regex, (match, prefix, suffix) => {
            modified = true;
            return `${prefix}'${kebabCase}'${suffix}`;
          });
        }
      });
    }

    return `style={{${newStyleContent}}}`;
  });

  // 也处理直接的对象属性（不在style中的）
  for (const [camelCase, kebabCase] of Object.entries(cssPropertyMap)) {
    // 匹配对象中的CSS属性
    const objPropRegex = new RegExp(`(\\{[^}]*\\s)${camelCase}(\\s*:)`, 'g');
    if (objPropRegex.test(content)) {
      content = content.replace(objPropRegex, (match, prefix, suffix) => {
        modified = true;
        return `${prefix}'${kebabCase}'${suffix}`;
      });
    }
  }

  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ Fixed CSS properties in: ${path.relative(process.cwd(), filePath)}`);
    return true;
  }

  return false;
}

function processDirectory(dir) {
  let fixedCount = 0;
  const files = fs.readdirSync(dir);
  
  for (const file of files) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory() && !file.startsWith('.') && file !== 'node_modules') {
      fixedCount += processDirectory(filePath);
    } else if (stat.isFile() && (file.endsWith('.tsx') || file.endsWith('.jsx'))) {
      if (fixCssPropertiesInFile(filePath)) {
        fixedCount++;
      }
    }
  }
  
  return fixedCount;
}

// 执行修复
console.log('🔧 Fixing CSS property names in TSX/JSX files...\n');
const srcDir = path.join(__dirname, 'src');
const fixedCount = processDirectory(srcDir);
console.log(`\n✨ Fixed ${fixedCount} files with CSS property issues.`);