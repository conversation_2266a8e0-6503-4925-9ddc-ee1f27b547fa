import{c as H,j as Ae,t as F,i as $,b as E,F as pe,d as w,e as t,f as i,S as ce,s as De,g as Ve}from"./index-Bmv3aZuw.js";class Be{constructor(a,l){this.parameterRanges=a,this.evaluateFunction=l}async optimize(a){const l=[],r=this.generateParameterCombinations();let s=0;for(const o of r){if(s>=a.maxIterations)break;try{const v=await this.evaluateFunction(o),h=this.extractScore(v,a.targetMetric);l.push({parameters:o,score:h,metrics:v}),s++}catch(v){console.warn("参数组合评估失败:",o,v)}}return l.sort((o,v)=>a.maximize?v.score-o.score:o.score-v.score),l}generateParameterCombinations(){const a=[],l=(r,s)=>{if(r>=this.parameterRanges.length){a.push({...s});return}const o=this.parameterRanges[r],v=o.step||(o.type==="int"?1:(o.max-o.min)/10);for(let h=o.min;h<=o.max;h+=v){const T=o.type==="int"?Math.round(h):h;s[o.name]=T,l(r+1,s)}};return l(0,{}),a}extractScore(a,l){return a[l]||0}}class Ne{constructor(a,l,r={}){this.parameterRanges=a,this.evaluateFunction=l,this.populationSize=r.populationSize||50,this.mutationRate=r.mutationRate||.1,this.crossoverRate=r.crossoverRate||.8}async optimize(a){let l=this.initializePopulation();const r=[];for(let s=0;s<a.maxIterations;s++){const o=await this.evaluatePopulation(l,a.targetMetric),v=o[0];r.push(v),l=this.evolvePopulation(o,a.maximize)}return r.sort((s,o)=>a.maximize?o.score-s.score:s.score-o.score)}initializePopulation(){const a=[];for(let l=0;l<this.populationSize;l++){const r={};for(const s of this.parameterRanges){const o=Math.random()*(s.max-s.min)+s.min;r[s.name]=s.type==="int"?Math.round(o):o}a.push(r)}return a}async evaluatePopulation(a,l){const r=[];for(const s of a)try{const o=await this.evaluateFunction(s),v=o[l]||0;r.push({parameters:s,score:v,metrics:o})}catch{r.push({parameters:s,score:-1/0,metrics:{}})}return r.sort((s,o)=>o.score-s.score)}evolvePopulation(a,l){const r=[],s=Math.floor(this.populationSize*.1);for(let o=0;o<s;o++)r.push({...a[o].parameters});for(;r.length<this.populationSize;){const o=this.selectParent(a),v=this.selectParent(a);let h=this.crossover(o.parameters,v.parameters);h=this.mutate(h),r.push(h)}return r}selectParent(a){const r=[];for(let s=0;s<3;s++){const o=Math.floor(Math.random()*a.length);r.push(a[o])}return r.sort((s,o)=>o.score-s.score)[0]}crossover(a,l){const r={};for(const s of this.parameterRanges)if(Math.random()<this.crossoverRate){const o=Math.random(),v=o*a[s.name]+(1-o)*l[s.name];r[s.name]=s.type==="int"?Math.round(v):v}else r[s.name]=Math.random()<.5?a[s.name]:l[s.name];return r}mutate(a){const l={...a};for(const r of this.parameterRanges)if(Math.random()<this.mutationRate){const s=(r.max-r.min)*.1,o=this.gaussianRandom()*s;let v=l[r.name]+o;v=Math.max(r.min,Math.min(r.max,v)),l[r.name]=r.type==="int"?Math.round(v):v}return l}gaussianRandom(){let a=0,l=0;for(;a===0;)a=Math.random();for(;l===0;)l=Math.random();return Math.sqrt(-2*Math.log(a))*Math.cos(2*Math.PI*l)}}class Ge{constructor(a,l){this.observations=[],this.parameterRanges=a,this.evaluateFunction=l}async optimize(a){const l=[],r=Math.min(10,a.maxIterations);for(let s=0;s<r;s++){const o=this.randomSample(),v=await this.evaluateFunction(o),h=v[a.targetMetric]||0;this.observations.push({params:o,score:h}),l.push({parameters:o,score:h,metrics:v})}for(let s=r;s<a.maxIterations;s++){const o=this.acquireNext(),v=await this.evaluateFunction(o),h=v[a.targetMetric]||0;this.observations.push({params:o,score:h}),l.push({parameters:o,score:h,metrics:v})}return l.sort((s,o)=>a.maximize?o.score-s.score:s.score-o.score)}randomSample(){const a={};for(const l of this.parameterRanges){const r=Math.random()*(l.max-l.min)+l.min;a[l.name]=l.type==="int"?Math.round(r):r}return a}acquireNext(){if(this.observations.length===0)return this.randomSample();const a=this.observations.reduce((r,s)=>s.score>r.score?s:r),l={};for(const r of this.parameterRanges){const s=a.params[r.name],o=(r.max-r.min)*.2,v=s+this.gaussianRandom()*o,h=Math.max(r.min,Math.min(r.max,v));l[r.name]=r.type==="int"?Math.round(h):h}return l}gaussianRandom(){let a=0,l=0;for(;a===0;)a=Math.random();for(;l===0;)l=Math.random();return Math.sqrt(-2*Math.log(a))*Math.cos(2*Math.PI*l)}}var Ye=F("<button>开始优化"),Je=F("<div><div>优化进行中... <!>%</div><div><div>"),Ke=F("<div><h4>最佳参数组合</h4><div></div><div><span>: </span><span>"),Qe=F("<div><h3>优化结果</h3><div>"),Ue=F("<div><div><h3>参数优化配置</h3><div><div><label>优化方法</label><select><option value=grid>网格搜索</option><option value=genetic>遗传算法</option><option value=bayesian>贝叶斯优化</option></select></div><div><label>目标指标</label><select><option value=sharpeRatio>夏普比率</option><option value=calmarRatio>卡尔马比率</option><option value=totalReturn>总收益率</option><option value=winRate>胜率</option></select></div><div><label>最大迭代次数</label><input type=number min=10 max=1000></div></div><div><div><h4>参数范围</h4><button>添加参数</button></div><div></div></div><div>"),Xe=F("<div><input type=text placeholder=参数名><input type=number placeholder=最小值 step=0.01><input type=number placeholder=最大值 step=0.01><input type=number placeholder=步长 step=0.01><select><option value=int>整数</option><option value=float>小数</option></select><button>×"),Ze=F("<button>停止优化"),et=F("<div><div></div><div>"),tt=F("<div><div><div></div><div></div></div><div>");function it(Q){const[a,l]=H("grid"),[r,s]=H("sharpeRatio"),[o,v]=H(100),[h,T]=H(!1),[G,Y]=H(0),[q,J]=H([]),[A,U]=H([{name:"shortPeriod",min:5,max:20,step:1,type:"int"},{name:"longPeriod",min:20,max:60,step:1,type:"int"},{name:"stopLoss",min:.02,max:.1,step:.01,type:"float"},{name:"takeProfit",min:.05,max:.2,step:.01,type:"float"}]),ve=()=>{U(y=>[...y,{name:`param${y.length+1}`,min:0,max:100,step:1,type:"int"}])},X=y=>{U(u=>u.filter((P,_)=>_!==y))},k=(y,u,P)=>{U(_=>_.map((j,L)=>L===y?{...j,[u]:P}:j))},D=async y=>{await new Promise(V=>setTimeout(V,100));const u=Math.random(),P=(u-.3)*.5,_=.1+u*.2,j=P/_,L=-u*.15,O=P/Math.abs(L);return{totalReturn:P,sharpeRatio:j,calmarRatio:O,volatility:_,maxDrawdown:L,winRate:.4+u*.4}},xe=async()=>{T(!0),Y(0),J([]);const y={method:a(),maxIterations:o(),targetMetric:r(),maximize:["sharpeRatio","calmarRatio","totalReturn","winRate"].includes(r())};try{let u;switch(a()){case"grid":u=new Be(A(),D);break;case"genetic":u=new Ne(A(),D);break;case"bayesian":u=new Ge(A(),D);break}const P=await u.optimize(y);J(P),Q.onOptimizationComplete?.(P)}catch(u){console.error("优化过程出错:",u)}finally{T(!1),Y(100)}},me=()=>{T(!1)},Z=Ae(()=>q()[0]);return(()=>{var y=Ue(),u=y.firstChild,P=u.firstChild,_=P.nextSibling,j=_.firstChild,L=j.firstChild,O=L.nextSibling,V=j.nextSibling,ee=V.firstChild,B=ee.nextSibling,se=V.nextSibling,le=se.firstChild,N=le.nextSibling,K=_.nextSibling,te=K.firstChild,ie=te.firstChild,oe=ie.nextSibling,de=te.nextSibling,ne=K.nextSibling;return O.addEventListener("change",n=>l(n.target.value)),B.addEventListener("change",n=>s(n.target.value)),N.addEventListener("change",n=>v(parseInt(n.target.value))),oe.$$click=ve,$(de,E(pe,{get each(){return A()},children:(n,p)=>(()=>{var C=Xe(),m=C.firstChild,e=m.nextSibling,g=e.nextSibling,x=g.nextSibling,b=x.nextSibling,I=b.nextSibling;return m.addEventListener("change",c=>k(p(),"name",c.target.value)),e.addEventListener("change",c=>k(p(),"min",parseFloat(c.target.value))),g.addEventListener("change",c=>k(p(),"max",parseFloat(c.target.value))),x.addEventListener("change",c=>k(p(),"step",parseFloat(c.target.value)||void 0)),b.addEventListener("change",c=>k(p(),"type",c.target.value)),I.$$click=()=>X(p()),w(c=>{var d=t({display:"grid",gridTemplateColumns:"1fr 1fr 1fr 1fr 80px 40px",gap:"8px",alignItems:"center",p:"12px",bg:"#fafafa",borderRadius:"8px"}),f=t({p:"6px 8px",border:"1px solid #d9d9d9",borderRadius:"4px",fontSize:"12px"}),z=t({p:"6px 8px",border:"1px solid #d9d9d9",borderRadius:"4px",fontSize:"12px"}),R=t({p:"6px 8px",border:"1px solid #d9d9d9",borderRadius:"4px",fontSize:"12px"}),M=t({p:"6px 8px",border:"1px solid #d9d9d9",borderRadius:"4px",fontSize:"12px"}),S=t({p:"6px 8px",border:"1px solid #d9d9d9",borderRadius:"4px",fontSize:"12px"}),W=t({p:"6px",bg:"#ff4d4f",color:"white",border:"none",borderRadius:"4px",fontSize:"12px",cursor:"pointer",_hover:{bg:"#ff7875"}});return d!==c.e&&i(C,c.e=d),f!==c.t&&i(m,c.t=f),z!==c.a&&i(e,c.a=z),R!==c.o&&i(g,c.o=R),M!==c.i&&i(x,c.i=M),S!==c.n&&i(b,c.n=S),W!==c.s&&i(I,c.s=W),c},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0}),w(()=>m.value=n.name),w(()=>e.value=n.min),w(()=>g.value=n.max),w(()=>x.value=n.step||""),w(()=>b.value=n.type),C})()})),$(ne,E(ce,{get when(){return!h()},get fallback(){return(()=>{var n=Ze();return n.$$click=me,w(()=>i(n,t({px:"24px",py:"12px",bg:"#ff4d4f",color:"white",border:"none",borderRadius:"8px",fontSize:"14px",fontWeight:"500",cursor:"pointer",_hover:{bg:"#ff7875"}}))),n})()},get children(){var n=Ye();return n.$$click=xe,w(p=>{var C=A().length===0,m=t({px:"24px",py:"12px",bg:"#52c41a",color:"white",border:"none",borderRadius:"8px",fontSize:"14px",fontWeight:"500",cursor:"pointer",_hover:{bg:"#73d13d"},_disabled:{bg:"#d9d9d9",cursor:"not-allowed"}});return C!==p.e&&(n.disabled=p.e=C),m!==p.t&&i(n,p.t=m),p},{e:void 0,t:void 0}),n}})),$(u,E(ce,{get when(){return h()},get children(){var n=Je(),p=n.firstChild,C=p.firstChild,m=C.nextSibling;m.nextSibling;var e=p.nextSibling,g=e.firstChild;return $(p,G,m),w(x=>{var b=t({mt:"16px",p:"16px",bg:"#f6ffed",borderRadius:"8px",border:"1px solid #b7eb8f"}),I=t({fontSize:"14px",color:"#52c41a",mb:"8px"}),c=t({width:"100%",height:"8px",bg:"#f0f0f0",borderRadius:"4px",overflow:"hidden"}),d=t({height:"100%",bg:"#52c41a",transition:"width 0.3s ease"}),f=`${G()}%`;return b!==x.e&&i(n,x.e=b),I!==x.t&&i(p,x.t=I),c!==x.a&&i(e,x.a=c),d!==x.o&&i(g,x.o=d),f!==x.i&&De(g,"width",x.i=f),x},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0}),n}}),null),$(y,E(ce,{get when(){return q().length>0},get children(){var n=Qe(),p=n.firstChild,C=p.nextSibling;return $(n,E(ce,{get when(){return Z()},get children(){var m=Ke(),e=m.firstChild,g=e.nextSibling,x=g.nextSibling,b=x.firstChild,I=b.firstChild,c=b.nextSibling;return $(g,E(pe,{get each(){return Object.entries(Z().parameters)},children:([d,f])=>(()=>{var z=et(),R=z.firstChild,M=R.nextSibling;return $(R,d),$(M,()=>typeof f=="number"?f.toFixed(3):f),w(S=>{var W=t({textAlign:"center"}),re=t({fontSize:"12px",color:"#8c8c8c",mb:"4px"}),ae=t({fontSize:"16px",fontWeight:"600",color:"#262626"});return W!==S.e&&i(z,S.e=W),re!==S.t&&i(R,S.t=re),ae!==S.a&&i(M,S.a=ae),S},{e:void 0,t:void 0,a:void 0}),z})()})),$(b,r,I),$(c,()=>Z().score.toFixed(4)),w(d=>{var f=t({p:"16px",bg:"#f6ffed",borderRadius:"8px",border:"1px solid #b7eb8f",mb:"20px"}),z=t({fontSize:"16px",fontWeight:"600",color:"#52c41a",margin:0,mb:"12px"}),R=t({display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(150px, 1fr))",gap:"12px"}),M=t({mt:"12px",textAlign:"center"}),S=t({fontSize:"14px",color:"#8c8c8c"}),W=t({fontSize:"18px",fontWeight:"700",color:"#52c41a",ml:"8px"});return f!==d.e&&i(m,d.e=f),z!==d.t&&i(e,d.t=z),R!==d.a&&i(g,d.a=R),M!==d.o&&i(x,d.o=M),S!==d.i&&i(b,d.i=S),W!==d.n&&i(c,d.n=W),d},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0}),m}}),C),$(C,E(pe,{get each(){return q().slice(0,10)},children:(m,e)=>(()=>{var g=tt(),x=g.firstChild,b=x.firstChild,I=b.nextSibling,c=x.nextSibling;return $(b,()=>e()+1),$(I,()=>Object.entries(m.parameters).map(([d,f])=>`${d}=${typeof f=="number"?f.toFixed(2):f}`).join(", ")),$(c,()=>m.score.toFixed(4)),w(d=>{var f=t({display:"flex",alignItems:"center",justifyContent:"space-between",p:"12px",borderBottom:"1px solid #f0f0f0",_hover:{bg:"#fafafa"}}),z=t({display:"flex",alignItems:"center",gap:"12px"}),R=t({width:"24px",height:"24px",bg:e()===0?"#52c41a":"#f0f0f0",color:e()===0?"white":"#8c8c8c",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",fontSize:"12px",fontWeight:"600"}),M=t({fontSize:"14px",color:"#262626"}),S=t({fontSize:"16px",fontWeight:"600",color:e()===0?"#52c41a":"#262626"});return f!==d.e&&i(g,d.e=f),z!==d.t&&i(x,d.t=z),R!==d.a&&i(b,d.a=R),M!==d.o&&i(I,d.o=M),S!==d.i&&i(c,d.i=S),d},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0}),g})()})),w(m=>{var e=t({bg:"white",borderRadius:"12px",p:"24px",boxShadow:"0 2px 8px rgba(0,0,0,0.1)"}),g=t({fontSize:"18px",fontWeight:"600",color:"#262626",margin:0,mb:"20px"}),x=t({maxHeight:"400px",overflowY:"auto"});return e!==m.e&&i(n,m.e=e),g!==m.t&&i(p,m.t=g),x!==m.a&&i(C,m.a=x),m},{e:void 0,t:void 0,a:void 0}),n}}),null),w(n=>{var p=t({display:"flex",flexDirection:"column",gap:"24px"}),C=t({bg:"white",borderRadius:"12px",p:"24px",boxShadow:"0 2px 8px rgba(0,0,0,0.1)"}),m=t({fontSize:"18px",fontWeight:"600",color:"#262626",margin:0,mb:"20px"}),e=t({display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"16px",mb:"24px"}),g=t({display:"block",fontSize:"14px",fontWeight:"500",color:"#262626",mb:"8px"}),x=t({width:"100%",p:"8px 12px",border:"1px solid #d9d9d9",borderRadius:"6px",fontSize:"14px"}),b=t({display:"block",fontSize:"14px",fontWeight:"500",color:"#262626",mb:"8px"}),I=t({width:"100%",p:"8px 12px",border:"1px solid #d9d9d9",borderRadius:"6px",fontSize:"14px"}),c=t({display:"block",fontSize:"14px",fontWeight:"500",color:"#262626",mb:"8px"}),d=t({width:"100%",p:"8px 12px",border:"1px solid #d9d9d9",borderRadius:"6px",fontSize:"14px"}),f=t({mb:"24px"}),z=t({display:"flex",alignItems:"center",justifyContent:"space-between",mb:"16px"}),R=t({fontSize:"16px",fontWeight:"600",color:"#262626",margin:0}),M=t({px:"12px",py:"6px",bg:"#1890ff",color:"white",border:"none",borderRadius:"6px",fontSize:"12px",cursor:"pointer",_hover:{bg:"#40a9ff"}}),S=t({display:"flex",flexDirection:"column",gap:"12px"}),W=t({display:"flex",gap:"12px"});return p!==n.e&&i(y,n.e=p),C!==n.t&&i(u,n.t=C),m!==n.a&&i(P,n.a=m),e!==n.o&&i(_,n.o=e),g!==n.i&&i(L,n.i=g),x!==n.n&&i(O,n.n=x),b!==n.s&&i(ee,n.s=b),I!==n.h&&i(B,n.h=I),c!==n.r&&i(le,n.r=c),d!==n.d&&i(N,n.d=d),f!==n.l&&i(K,n.l=f),z!==n.u&&i(te,n.u=z),R!==n.c&&i(ie,n.c=R),M!==n.w&&i(oe,n.w=M),S!==n.m&&i(de,n.m=S),W!==n.f&&i(ne,n.f=W),n},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0,u:void 0,c:void 0,w:void 0,m:void 0,f:void 0}),w(()=>O.value=a()),w(()=>B.value=r()),w(()=>N.value=o()),y})()}Ve(["click"]);var ot=F("<div><div><h1>🔧 参数优化</h1><p>使用先进的优化算法找到策略的最佳参数组合</p></div><div><div><div><div><span>🔍</span></div><h3>网格搜索</h3></div><p>系统性地搜索参数空间的每个组合，保证找到全局最优解，适合参数较少的情况。</p></div><div><div><div><span>🧬</span></div><h3>遗传算法</h3></div><p>模拟生物进化过程，通过选择、交叉和变异操作寻找最优解，适合复杂的多参数优化。</p></div><div><div><div><span>🎯</span></div><h3>贝叶斯优化</h3></div><p>基于概率模型的智能搜索，能够在较少的评估次数下找到最优解，适合计算成本高的场景。</p></div></div><div><h3>💡 优化建议</h3><div><div><h4>参数设置</h4><ul><li>合理设置参数范围，避免过大或过小</li><li>考虑参数之间的约束关系</li><li>使用合适的步长，平衡精度和效率</li></ul></div><div><h4>目标选择</h4><ul><li>夏普比率：综合考虑收益和风险</li><li>卡尔马比率：关注回撤控制</li><li>总收益率：追求绝对收益</li></ul></div><div><h4>方法选择</h4><ul><li>参数少（≤4个）：使用网格搜索</li><li>参数多（5-10个）：使用遗传算法</li><li>计算成本高：使用贝叶斯优化</li></ul></div></div></div><div><div><span>⚠️</span><h4>注意事项</h4></div><p>参数优化存在过拟合风险，建议使用样本外数据验证优化结果。优化后的参数可能在历史数据上表现良好，但在未来市场中未必有效。 建议结合多个时间段的数据进行验证，并定期重新优化参数。");function rt(){const[Q,a]=H([]),[l,r]=H(!1),s=o=>{a(o),r(!0)};return(()=>{var o=ot(),v=o.firstChild,h=v.firstChild,T=h.nextSibling,G=v.nextSibling,Y=G.firstChild,q=Y.firstChild,J=q.firstChild,A=J.firstChild,U=J.nextSibling,ve=q.nextSibling,X=Y.nextSibling,k=X.firstChild,D=k.firstChild,xe=D.firstChild,me=D.nextSibling,Z=k.nextSibling,y=X.nextSibling,u=y.firstChild,P=u.firstChild,_=P.firstChild,j=P.nextSibling,L=u.nextSibling,O=G.nextSibling,V=O.firstChild,ee=V.nextSibling,B=ee.firstChild,se=B.firstChild,le=se.nextSibling,N=B.nextSibling,K=N.firstChild,te=K.nextSibling,ie=N.nextSibling,oe=ie.firstChild,de=oe.nextSibling,ne=O.nextSibling,n=ne.firstChild,p=n.firstChild,C=p.nextSibling,m=n.nextSibling;return $(o,E(it,{onOptimizationComplete:s}),O),w(e=>{var g=t({display:"flex",flexDirection:"column",gap:"24px",maxWidth:"1200px",margin:"0 auto",p:"24px"}),x=t({textAlign:"center",mb:"20px"}),b=t({fontSize:"28px",fontWeight:"700",color:"#262626",margin:0,mb:"8px"}),I=t({fontSize:"16px",color:"#8c8c8c",margin:0}),c=t({display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(300px, 1fr))",gap:"16px",mb:"24px"}),d=t({bg:"white",borderRadius:"12px",p:"20px",boxShadow:"0 2px 8px rgba(0,0,0,0.1)",border:"1px solid #f0f0f0"}),f=t({display:"flex",alignItems:"center",mb:"12px"}),z=t({width:"40px",height:"40px",bg:"#1890ff",borderRadius:"8px",display:"flex",alignItems:"center",justifyContent:"center",mr:"12px"}),R=t({fontSize:"20px",color:"white"}),M=t({fontSize:"16px",fontWeight:"600",color:"#262626",margin:0}),S=t({fontSize:"14px",color:"#8c8c8c",lineHeight:"1.5",margin:0}),W=t({bg:"white",borderRadius:"12px",p:"20px",boxShadow:"0 2px 8px rgba(0,0,0,0.1)",border:"1px solid #f0f0f0"}),re=t({display:"flex",alignItems:"center",mb:"12px"}),ae=t({width:"40px",height:"40px",bg:"#52c41a",borderRadius:"8px",display:"flex",alignItems:"center",justifyContent:"center",mr:"12px"}),he=t({fontSize:"20px",color:"white"}),fe=t({fontSize:"16px",fontWeight:"600",color:"#262626",margin:0}),ge=t({fontSize:"14px",color:"#8c8c8c",lineHeight:"1.5",margin:0}),ue=t({bg:"white",borderRadius:"12px",p:"20px",boxShadow:"0 2px 8px rgba(0,0,0,0.1)",border:"1px solid #f0f0f0"}),be=t({display:"flex",alignItems:"center",mb:"12px"}),Se=t({width:"40px",height:"40px",bg:"#722ed1",borderRadius:"8px",display:"flex",alignItems:"center",justifyContent:"center",mr:"12px"}),ze=t({fontSize:"20px",color:"white"}),Re=t({fontSize:"16px",fontWeight:"600",color:"#262626",margin:0}),we=t({fontSize:"14px",color:"#8c8c8c",lineHeight:"1.5",margin:0}),ye=t({bg:"white",borderRadius:"12px",p:"24px",boxShadow:"0 2px 8px rgba(0,0,0,0.1)"}),Ce=t({fontSize:"18px",fontWeight:"600",color:"#262626",margin:0,mb:"16px"}),Me=t({display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(250px, 1fr))",gap:"16px"}),Pe=t({p:"16px",bg:"#f6ffed",borderRadius:"8px",border:"1px solid #b7eb8f"}),Ie=t({fontSize:"14px",fontWeight:"600",color:"#52c41a",margin:0,mb:"8px"}),$e=t({fontSize:"12px",color:"#8c8c8c",lineHeight:"1.5",margin:0,paddingLeft:"16px"}),We=t({p:"16px",bg:"#fff7e6",borderRadius:"8px",border:"1px solid #ffd591"}),_e=t({fontSize:"14px",fontWeight:"600",color:"#fa8c16",margin:0,mb:"8px"}),Oe=t({fontSize:"12px",color:"#8c8c8c",lineHeight:"1.5",margin:0,paddingLeft:"16px"}),Fe=t({p:"16px",bg:"#f0f5ff",borderRadius:"8px",border:"1px solid #adc6ff"}),ke=t({fontSize:"14px",fontWeight:"600",color:"#1890ff",margin:0,mb:"8px"}),je=t({fontSize:"12px",color:"#8c8c8c",lineHeight:"1.5",margin:0,paddingLeft:"16px"}),Le=t({bg:"#fff2f0",borderRadius:"8px",p:"16px",border:"1px solid #ffccc7"}),He=t({display:"flex",alignItems:"center",mb:"8px"}),Ee=t({fontSize:"16px",mr:"8px"}),Te=t({fontSize:"14px",fontWeight:"600",color:"#ff4d4f",margin:0}),qe=t({fontSize:"12px",color:"#8c8c8c",lineHeight:"1.5",margin:0});return g!==e.e&&i(o,e.e=g),x!==e.t&&i(v,e.t=x),b!==e.a&&i(h,e.a=b),I!==e.o&&i(T,e.o=I),c!==e.i&&i(G,e.i=c),d!==e.n&&i(Y,e.n=d),f!==e.s&&i(q,e.s=f),z!==e.h&&i(J,e.h=z),R!==e.r&&i(A,e.r=R),M!==e.d&&i(U,e.d=M),S!==e.l&&i(ve,e.l=S),W!==e.u&&i(X,e.u=W),re!==e.c&&i(k,e.c=re),ae!==e.w&&i(D,e.w=ae),he!==e.m&&i(xe,e.m=he),fe!==e.f&&i(me,e.f=fe),ge!==e.y&&i(Z,e.y=ge),ue!==e.g&&i(y,e.g=ue),be!==e.p&&i(u,e.p=be),Se!==e.b&&i(P,e.b=Se),ze!==e.T&&i(_,e.T=ze),Re!==e.A&&i(j,e.A=Re),we!==e.O&&i(L,e.O=we),ye!==e.I&&i(O,e.I=ye),Ce!==e.S&&i(V,e.S=Ce),Me!==e.W&&i(ee,e.W=Me),Pe!==e.C&&i(B,e.C=Pe),Ie!==e.B&&i(se,e.B=Ie),$e!==e.v&&i(le,e.v=$e),We!==e.k&&i(N,e.k=We),_e!==e.x&&i(K,e.x=_e),Oe!==e.j&&i(te,e.j=Oe),Fe!==e.q&&i(ie,e.q=Fe),ke!==e.z&&i(oe,e.z=ke),je!==e.P&&i(de,e.P=je),Le!==e.H&&i(ne,e.H=Le),He!==e.F&&i(n,e.F=He),Ee!==e.M&&i(p,e.M=Ee),Te!==e.D&&i(C,e.D=Te),qe!==e.R&&i(m,e.R=qe),e},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0,u:void 0,c:void 0,w:void 0,m:void 0,f:void 0,y:void 0,g:void 0,p:void 0,b:void 0,T:void 0,A:void 0,O:void 0,I:void 0,S:void 0,W:void 0,C:void 0,B:void 0,v:void 0,k:void 0,x:void 0,j:void 0,q:void 0,z:void 0,P:void 0,H:void 0,F:void 0,M:void 0,D:void 0,R:void 0}),o})()}export{rt as default};
