import { createSignal, Show, onMount, createEffect } from 'solid-js'
import { css } from '../../styled-system/css'
import OrderForm from './OrderForm'
import OrderManagement from './OrderManagement'
import PositionManagement from './PositionManagement'
// import { RealTimeTicker } from '../RealTimeTicker'
// import { EnhancedChart } from '../EnhancedChart'
import { useAtom } from 'jotai'
import { tradingAtom, marketDataAtom, userAtom } from '../../stores/atoms'

interface Props {
  defaultSymbol?: string
  layout?: 'default' | 'compact' | 'fullscreen'
}

export function TradingTerminal(props: Props) {
  // State management
  const [trading, setTrading] = useAtom(tradingAtom)
  const [marketData] = useAtom(marketDataAtom)
  const [user] = useAtom(userAtom)

  // Local state
  const [activeTab, setActiveTab] = createSignal<'order' | 'positions' | 'orders'>('order')
  const [selectedSymbol, setSelectedSymbol] = createSignal(props.defaultSymbol || 'IF2312')
  const [chartVisible, setChartVisible] = createSignal(true)
  const [tickerVisible, setTickerVisible] = createSignal(true)
  const [isFullscreen, setIsFullscreen] = createSignal(false)

  // Chart data for the selected symbol
  const chartData = () => {
    return marketData.charts?.[selectedSymbol()] || []
  }

  // Current price for the selected symbol
  const currentPrice = () => {
    return marketData.realTime?.[selectedSymbol()]?.price || 0
  }

  // Trading handlers
  const handleOrderSubmit = (orderData: any) => {
    console.log('Submitting order:', orderData)
    // In a real implementation, this would call the trading API
    
    // Mock order submission
    const newOrder = {
      id: `order_${Date.now()}`,
      ...orderData,
      status: 'pending',
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString()
    }

    // Add to orders (would be handled by store in real implementation)
    console.log('New order created:', newOrder)
  }

  const handleStockSelect = (stock: any) => {
    setSelectedSymbol(stock.symbol)
    console.log('Selected stock:', stock)
  }

  const handleOrderCancel = (orderNo: string) => {
    console.log('Canceling order:', orderNo)
  }

  const handlePositionClose = (positionId: string) => {
    console.log('Closing position:', positionId)
  }

  const handlePartialClose = (positionId: string, quantity: number) => {
    console.log('Partial close position:', positionId, 'quantity:', quantity)
  }

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen())
  }

  const toggleChart = () => {
    setChartVisible(!chartVisible())
  }

  const toggleTicker = () => {
    setTickerVisible(!tickerVisible())
  }

  // Layout styles based on props
  const getLayoutClass = () => {
    switch (props.layout) {
      case 'compact':
        return css({
          display: 'grid',
          'grid-template-columns': '1fr 400px',
          gap: '16px',
          height: '600px'
        })
      case 'fullscreen':
        return css({
          display: 'grid',
          'grid-template-columns': '1fr 500px',
          gridTemplateRows: 'auto 1fr',
          gap: '16px',
          height: '100vh',
          padding: '16px'
        })
      default:
        return css({
          display: 'grid',
          'grid-template-columns': '1fr 450px',
          gridTemplateRows: 'auto auto 1fr',
          gap: '16px',
          height: '800px'
        })
    }
  }

  const getMobileLayoutClass = () => {
    return css({
      display: 'flex',
      'flex-direction': 'column',
      gap: '16px',
      height: 'auto'
    })
  }

  return (
    <div class={css({
      padding: '20px',
      bg: '#f5f7fa',
      'min-height': '100vh'
    })}>
      {/* Header */}
      <div class={css({
        display: 'flex',
        'justify-content': 'space-between',
        'align-items': 'center',
        'margin-bottom': '20px',
        padding: '16px 20px',
        bg: 'white',
        'border-radius': '8px',
        'box-shadow': '0 2px 8px rgba(0, 0, 0, 0.1)'
      })}>
        <div class={css({ display: 'flex', 'align-items': 'center', gap: '16px' })}>
          <h1 class={css({ margin: 0, 'font-size': '24px', 'font-weight': 'bold', color: '#303133' })}>
            交易终端
          </h1>
          <div class={css({
            padding: '4px 12px',
            bg: '#f0f9ff',
            color: '#1d4ed8',
            'border-radius': '16px',
            'font-size': '12px',
            'font-weight': 'bold'
          })}>
            {selectedSymbol()}
          </div>
        </div>

        <div class={css({ display: 'flex', 'align-items': 'center', gap: '8px' })}>
          <button
            onClick={toggleTicker}
            class={css({
              padding: '6px 12px',
              border: '1px solid #dcdfe6',
              'border-radius': '4px',
              'font-size': '12px',
              cursor: 'pointer',
              bg: tickerVisible() ? '#409eff' : 'white',
              color: tickerVisible() ? 'white' : '#606266'
            })}
          >
            行情
          </button>
          <button
            onClick={toggleChart}
            class={css({
              padding: '6px 12px',
              border: '1px solid #dcdfe6',
              'border-radius': '4px',
              'font-size': '12px',
              cursor: 'pointer',
              bg: chartVisible() ? '#409eff' : 'white',
              color: chartVisible() ? 'white' : '#606266'
            })}
          >
            图表
          </button>
          <button
            onClick={toggleFullscreen}
            class={css({
              padding: '6px 12px',
              border: '1px solid #dcdfe6',
              'border-radius': '4px',
              'font-size': '12px',
              cursor: 'pointer',
              bg: 'white',
              color: '#606266'
            })}
          >
            {isFullscreen() ? '退出全屏' : '全屏'}
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div class={css({
        '@media (min-width: 768px)': getLayoutClass(),
        '@media (max-width: 767px)': getMobileLayoutClass()
      })}>
        {/* Left Panel - Charts and Market Data */}
        <div class={css({ display: 'flex', 'flex-direction': 'column', gap: '16px' })}>
          {/* Real-time Ticker */}
          <Show when={tickerVisible()}>
            <div class={css({
              bg: 'white',
              'border-radius': '8px',
              padding: '16px',
              'box-shadow': '0 2px 8px rgba(0, 0, 0, 0.1)'
            })}>
              <RealTimeTicker
                symbols={[selectedSymbol()]}
                showChart={false}
                layout="horizontal"
              />
            </div>
          </Show>

          {/* Chart */}
          <Show when={chartVisible()}>
            <div class={css({
              bg: 'white',
              'border-radius': '8px',
              padding: '16px',
              'box-shadow': '0 2px 8px rgba(0, 0, 0, 0.1)',
              height: props.layout === 'compact' ? '300px' : '400px'
            })}>
              <EnhancedChart
                symbol={selectedSymbol()}
                height={props.layout === 'compact' ? 300 : 400}
                showToolbar={true}
                showIndicators={true}
              />
            </div>
          </Show>

          {/* Trading Management Tabs - Only on mobile */}
          <div class={css({
            '@media (min-width: 768px)': { display: 'none' },
            '@media (max-width: 767px)': { display: 'block' }
          })}>
            <div class={css({
              bg: 'white',
              'border-radius': '8px',
              'box-shadow': '0 2px 8px rgba(0, 0, 0, 0.1)'
            })}>
              {/* Tab Headers */}
              <div class={css({
                display: 'flex',
                'border-bottom': '1px solid #ebeef5'
              })}>
                <button
                  onClick={() => setActiveTab('order')}
                  class={css({
                    flex: 1,
                    padding: '12px 16px',
                    border: 'none',
                    bg: activeTab() === 'order' ? '#409eff' : 'transparent',
                    color: activeTab() === 'order' ? 'white' : '#606266',
                    'font-size': '14px',
                    cursor: 'pointer',
                    'border-radius': activeTab() === 'order' ? '8px 8px 0 0' : '0'
                  })}
                >
                  下单
                </button>
                <button
                  onClick={() => setActiveTab('positions')}
                  class={css({
                    flex: 1,
                    padding: '12px 16px',
                    border: 'none',
                    bg: activeTab() === 'positions' ? '#409eff' : 'transparent',
                    color: activeTab() === 'positions' ? 'white' : '#606266',
                    'font-size': '14px',
                    cursor: 'pointer',
                    'border-radius': activeTab() === 'positions' ? '8px 8px 0 0' : '0'
                  })}
                >
                  持仓
                </button>
                <button
                  onClick={() => setActiveTab('orders')}
                  class={css({
                    flex: 1,
                    padding: '12px 16px',
                    border: 'none',
                    bg: activeTab() === 'orders' ? '#409eff' : 'transparent',
                    color: activeTab() === 'orders' ? 'white' : '#606266',
                    'font-size': '14px',
                    cursor: 'pointer',
                    'border-radius': activeTab() === 'orders' ? '8px 8px 0 0' : '0'
                  })}
                >
                  订单
                </button>
              </div>

              {/* Tab Content */}
              <div class={css({ padding: '0' })}>
                <Show when={activeTab() === 'order'}>
                  <OrderForm
                    defaultSymbol={selectedSymbol()}
                    onSubmit={handleOrderSubmit}
                    onStockSelect={handleStockSelect}
                  />
                </Show>
                <Show when={activeTab() === 'positions'}>
                  <PositionManagement
                    onPositionClose={handlePositionClose}
                    onPartialClose={handlePartialClose}
                  />
                </Show>
                <Show when={activeTab() === 'orders'}>
                  <OrderManagement
                    onOrderCancel={handleOrderCancel}
                  />
                </Show>
              </div>
            </div>
          </div>
        </div>

        {/* Right Panel - Trading Forms and Management (Desktop only) */}
        <div class={css({
          '@media (max-width: 767px)': { display: 'none' },
          '@media (min-width: 768px)': {
            display: 'flex',
            'flex-direction': 'column',
            gap: '16px'
          }
        })}>
          {/* Order Form */}
          <div class={css({
            bg: 'white',
            'border-radius': '8px',
            'box-shadow': '0 2px 8px rgba(0, 0, 0, 0.1)'
          })}>
            <OrderForm
              defaultSymbol={selectedSymbol()}
              onSubmit={handleOrderSubmit}
              onStockSelect={handleStockSelect}
            />
          </div>

          {/* Management Tabs */}
          <div class={css({
            bg: 'white',
            'border-radius': '8px',
            'box-shadow': '0 2px 8px rgba(0, 0, 0, 0.1)',
            flex: 1
          })}>
            {/* Tab Headers */}
            <div class={css({
              display: 'flex',
              'border-bottom': '1px solid #ebeef5'
            })}>
              <button
                onClick={() => setActiveTab('positions')}
                class={css({
                  flex: 1,
                  padding: '12px 16px',
                  border: 'none',
                  bg: activeTab() === 'positions' ? '#409eff' : 'transparent',
                  color: activeTab() === 'positions' ? 'white' : '#606266',
                  'font-size': '14px',
                  cursor: 'pointer',
                  'border-radius': activeTab() === 'positions' ? '8px 8px 0 0' : '0'
                })}
              >
                持仓管理
              </button>
              <button
                onClick={() => setActiveTab('orders')}
                class={css({
                  flex: 1,
                  padding: '12px 16px',
                  border: 'none',
                  bg: activeTab() === 'orders' ? '#409eff' : 'transparent',
                  color: activeTab() === 'orders' ? 'white' : '#606266',
                  'font-size': '14px',
                  cursor: 'pointer',
                  'border-radius': activeTab() === 'orders' ? '8px 8px 0 0' : '0'
                })}
              >
                订单管理
              </button>
            </div>

            {/* Tab Content */}
            <div class={css({ 
              padding: '0',
              height: 'calc(100% - 50px)',
              overflow: 'hidden'
            })}>
              <Show when={activeTab() === 'positions'}>
                <PositionManagement
                  onPositionClose={handlePositionClose}
                  onPartialClose={handlePartialClose}
                />
              </Show>
              <Show when={activeTab() === 'orders'}>
                <OrderManagement
                  onOrderCancel={handleOrderCancel}
                />
              </Show>
            </div>
          </div>
        </div>
      </div>

      {/* Status Bar */}
      <div class={css({
        'margin-top': '20px',
        padding: '12px 20px',
        bg: 'white',
        'border-radius': '8px',
        'box-shadow': '0 2px 8px rgba(0, 0, 0, 0.1)',
        display: 'flex',
        'justify-content': 'space-between',
        'align-items': 'center',
        'font-size': '14px',
        color: '#606266'
      })}>
        <div class={css({ display: 'flex', gap: '20px' })}>
          <span>连接状态: <span class={css({ color: '#67c23a' })}>已连接</span></span>
          <span>账户: {user.profile?.name || '未登录'}</span>
          <span>可用资金: {trading.account?.availableCash?.toLocaleString() || '0'}</span>
        </div>
        <div>
          <span>交易时间: {new Date().toLocaleTimeString()}</span>
        </div>
      </div>
    </div>
  )
}

export default TradingTerminal