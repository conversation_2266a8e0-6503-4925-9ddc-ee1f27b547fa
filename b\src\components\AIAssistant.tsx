/**
 * AI 辅助组件
 * 使用 Transformers.js 提供本地 AI 功能
 */

import { createSignal, createEffect, onMount, Show, For } from 'solid-js';

interface AIMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: number;
}

interface AIAssistantProps {
  onCodeGenerate?: (code: string) => void;
  onCodeOptimize?: (code: string) => void;
  currentCode?: string;
  theme?: 'light' | 'dark';
}

export default function AIAssistant(props: AIAssistantProps) {
  const [isOpen, setIsOpen] = createSignal(false);
  const [messages, setMessages] = createSignal<AIMessage[]>([]);
  const [inputValue, setInputValue] = createSignal('');
  const [isLoading, setIsLoading] = createSignal(false);
  const [isModelLoading, setIsModelLoading] = createSignal(false);
  const [modelReady, setModelReady] = createSignal(false);

  // 模拟的 AI 模型（实际项目中会使用 Transformers.js）
  let aiModel: any = null;

  // 预定义的策略模板
  const strategyTemplates = {
    'moving_average': {
      name: '移动平均策略',
      description: '基于移动平均线的趋势跟踪策略',
      code: `import pandas as pd
import numpy as np

def moving_average_strategy(data, short_window=5, long_window=20):
    """
    移动平均策略
    当短期均线上穿长期均线时买入，下穿时卖出
    """
    # 计算移动平均线
    data['MA_short'] = data['close'].rolling(window=short_window).mean()
    data['MA_long'] = data['close'].rolling(window=long_window).mean()
    
    # 生成交易信号
    data['signal'] = 0
    data['signal'][short_window:] = np.where(
        data['MA_short'][short_window:] > data['MA_long'][short_window:], 1, 0
    )
    
    # 计算持仓变化
    data['position'] = data['signal'].diff()
    
    return data

# 策略参数
SHORT_WINDOW = 5
LONG_WINDOW = 20

# 执行策略
def execute_strategy(data):
    return moving_average_strategy(data, SHORT_WINDOW, LONG_WINDOW)`
    },
    'mean_reversion': {
      name: '均值回归策略',
      description: '基于价格偏离均值的反转策略',
      code: `import pandas as pd
import numpy as np

def mean_reversion_strategy(data, window=20, threshold=2):
    """
    均值回归策略
    当价格偏离均值超过阈值时进行反向交易
    """
    # 计算移动平均和标准差
    data['MA'] = data['close'].rolling(window=window).mean()
    data['std'] = data['close'].rolling(window=window).std()
    
    # 计算Z分数
    data['z_score'] = (data['close'] - data['MA']) / data['std']
    
    # 生成交易信号
    data['signal'] = 0
    data['signal'] = np.where(data['z_score'] > threshold, -1,  # 卖出信号
                     np.where(data['z_score'] < -threshold, 1, 0))  # 买入信号
    
    return data

# 策略参数
WINDOW = 20
THRESHOLD = 2

# 执行策略
def execute_strategy(data):
    return mean_reversion_strategy(data, WINDOW, THRESHOLD)`
    },
    'rsi': {
      name: 'RSI 策略',
      description: '基于相对强弱指标的超买超卖策略',
      code: `import pandas as pd
import numpy as np

def calculate_rsi(data, window=14):
    """计算RSI指标"""
    delta = data['close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    return rsi

def rsi_strategy(data, rsi_window=14, oversold=30, overbought=70):
    """
    RSI策略
    RSI < 30 时买入，RSI > 70 时卖出
    """
    # 计算RSI
    data['RSI'] = calculate_rsi(data, rsi_window)
    
    # 生成交易信号
    data['signal'] = 0
    data['signal'] = np.where(data['RSI'] < oversold, 1,  # 买入信号
                     np.where(data['RSI'] > overbought, -1, 0))  # 卖出信号
    
    return data

# 策略参数
RSI_WINDOW = 14
OVERSOLD = 30
OVERBOUGHT = 70

# 执行策略
def execute_strategy(data):
    return rsi_strategy(data, RSI_WINDOW, OVERSOLD, OVERBOUGHT)`
    }
  };

  // 初始化 AI 模型（模拟）
  const initializeAI = async () => {
    setIsModelLoading(true);
    try {
      // 模拟模型加载
      await new Promise(resolve => setTimeout(resolve, 2000));
      aiModel = { ready: true }; // 模拟模型
      setModelReady(true);
      
      // 添加欢迎消息
      addMessage('assistant', '你好！我是 AI 策略助手。我可以帮你：\n\n1. 生成量化交易策略代码\n2. 优化现有策略\n3. 解释策略逻辑\n4. 提供策略建议\n\n请告诉我你需要什么帮助？');
    } catch (error) {
      console.error('AI model initialization failed:', error);
      addMessage('assistant', '抱歉，AI 模型加载失败。请刷新页面重试。');
    } finally {
      setIsModelLoading(false);
    }
  };

  // 添加消息
  const addMessage = (type: 'user' | 'assistant', content: string) => {
    const newMessage: AIMessage = {
      id: Date.now().toString(),
      type,
      content,
      timestamp: Date.now()
    };
    setMessages(prev => [...prev, newMessage]);
  };

  // 处理用户输入
  const handleSendMessage = async () => {
    const input = inputValue().trim();
    if (!input || isLoading()) return;

    addMessage('user', input);
    setInputValue('');
    setIsLoading(true);

    try {
      // 模拟 AI 响应
      const response = await generateAIResponse(input);
      addMessage('assistant', response);
    } catch (error) {
      addMessage('assistant', '抱歉，处理您的请求时出现错误。请重试。');
    } finally {
      setIsLoading(false);
    }
  };

  // 生成 AI 响应（模拟）
  const generateAIResponse = async (input: string): Promise<string> => {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

    const lowerInput = input.toLowerCase();

    // 策略生成请求
    if (lowerInput.includes('生成') || lowerInput.includes('创建') || lowerInput.includes('策略')) {
      if (lowerInput.includes('移动平均') || lowerInput.includes('均线')) {
        return `我为您生成了一个移动平均策略：\n\n${strategyTemplates.moving_average.code}\n\n这个策略使用短期和长期移动平均线的交叉来生成买卖信号。您可以根据需要调整参数。`;
      } else if (lowerInput.includes('均值回归') || lowerInput.includes('回归')) {
        return `我为您生成了一个均值回归策略：\n\n${strategyTemplates.mean_reversion.code}\n\n这个策略基于价格偏离均值的程度来进行反向交易。`;
      } else if (lowerInput.includes('rsi') || lowerInput.includes('相对强弱')) {
        return `我为您生成了一个RSI策略：\n\n${strategyTemplates.rsi.code}\n\n这个策略使用RSI指标来识别超买超卖机会。`;
      } else {
        return `我可以为您生成以下类型的策略：\n\n1. 移动平均策略 - 趋势跟踪\n2. 均值回归策略 - 价格反转\n3. RSI策略 - 超买超卖\n\n请告诉我您想要哪种类型的策略？`;
      }
    }

    // 代码优化请求
    if (lowerInput.includes('优化') || lowerInput.includes('改进')) {
      return `我可以帮您优化代码：\n\n1. 添加风险管理机制\n2. 优化参数设置\n3. 提高执行效率\n4. 增加错误处理\n\n请分享您的代码，我会为您提供具体的优化建议。`;
    }

    // 解释请求
    if (lowerInput.includes('解释') || lowerInput.includes('说明')) {
      return `我可以解释策略的各个部分：\n\n1. 技术指标计算\n2. 信号生成逻辑\n3. 风险控制机制\n4. 参数含义\n\n请告诉我您想了解哪个部分？`;
    }

    // 默认响应
    return `我理解您的问题。作为量化策略助手，我可以帮您：\n\n• 生成各种类型的交易策略\n• 优化现有策略代码\n• 解释策略逻辑和参数\n• 提供风险管理建议\n\n请具体告诉我您需要什么帮助？`;
  };

  // 快速操作
  const quickActions = [
    { label: '生成移动平均策略', action: () => setInputValue('请生成一个移动平均策略') },
    { label: '生成RSI策略', action: () => setInputValue('请生成一个RSI策略') },
    { label: '优化当前代码', action: () => setInputValue('请帮我优化当前的策略代码') },
    { label: '解释策略逻辑', action: () => setInputValue('请解释这个策略的逻辑') }
  ];

  onMount(() => {
    initializeAI();
  });

  return (
    <>
      {/* AI 助手按钮 */}
      <button
        style={{
          position: 'fixed',
          bottom: '20px',
          right: '20px',
          width: '60px',
          height: '60px',
          'border-radius': '50%',
          'background-color': '#3b82f6',
          color: 'white',
          border: 'none',
          cursor: 'pointer',
          'box-shadow': '0 4px 12px rgba(59, 130, 246, 0.3)',
          'font-size': '24px',
          'z-index': 1000,
          transition: 'all 0.3s ease'
        }}
        onClick={() => setIsOpen(!isOpen())}
        onMouseEnter={(e) => {
          e.target.style.transform = 'scale(1.1)';
        }}
        onMouseLeave={(e) => {
          e.target.style.transform = 'scale(1)';
        }}
      >
        🤖
      </button>

      {/* AI 助手面板 */}
      <Show when={isOpen()}>
        <div style={{
          position: 'fixed',
          bottom: '100px',
          right: '20px',
          width: '400px',
          height: '600px',
          'background-color': props.theme === 'dark' ? '#1f2937' : 'white',
          border: '1px solid #d1d5db',
          'border-radius': '12px',
          'box-shadow': '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
          'z-index': 1000,
          display: 'flex',
          'flex-direction': 'column',
          overflow: 'hidden'
        }}>
          {/* 头部 */}
          <div style={{
            padding: '16px',
            'border-bottom': '1px solid #e5e7eb',
            display: 'flex',
            'justify-content': 'space-between',
            'align-items': 'center',
            'background-color': props.theme === 'dark' ? '#374151' : '#f9fafb'
          }}>
            <h3 style={{
              margin: '0',
              'font-size': '16px',
              'font-weight': '600',
              color: props.theme === 'dark' ? '#f3f4f6' : '#111827'
            }}>
              🤖 AI 策略助手
            </h3>
            <button
              style={{
                background: 'none',
                border: 'none',
                'font-size': '20px',
                cursor: 'pointer',
                color: props.theme === 'dark' ? '#9ca3af' : '#6b7280'
              }}
              onClick={() => setIsOpen(false)}
            >
              ×
            </button>
          </div>

          {/* 消息区域 */}
          <div style={{
            flex: 1,
            padding: '16px',
            'overflow-y': 'auto',
            display: 'flex',
            'flex-direction': 'column',
            gap: '12px'
          }}>
            <Show when={isModelLoading()}>
              <div style={{
                'text-align': 'center',
                padding: '20px',
                color: props.theme === 'dark' ? '#9ca3af' : '#6b7280'
              }}>
                <div style={{
                  width: '40px',
                  height: '40px',
                  border: '3px solid #e5e7eb',
                  'border-top': '3px solid #3b82f6',
                  'border-radius': '50%',
                  animation: 'spin 1s linear infinite',
                  margin: '0 auto 12px'
                }} />
                正在加载 AI 模型...
              </div>
            </Show>

            <For each={messages()}>
              {(message) => (
                <div style={{
                  display: 'flex',
                  'justify-content': message.type === 'user' ? 'flex-end' : 'flex-start'
                }}>
                  <div style={{
                    'max-width': '80%',
                    padding: '8px 12px',
                    'border-radius': '12px',
                    'background-color': message.type === 'user' 
                      ? '#3b82f6' 
                      : props.theme === 'dark' ? '#374151' : '#f3f4f6',
                    color: message.type === 'user' 
                      ? 'white' 
                      : props.theme === 'dark' ? '#f3f4f6' : '#111827',
                    'font-size': '14px',
                    'line-height': '1.4',
                    'white-space': 'pre-wrap'
                  }}>
                    {message.content}
                  </div>
                </div>
              )}
            </For>

            <Show when={isLoading()}>
              <div style={{
                display: 'flex',
                'justify-content': 'flex-start'
              }}>
                <div style={{
                  padding: '8px 12px',
                  'border-radius': '12px',
                  'background-color': props.theme === 'dark' ? '#374151' : '#f3f4f6',
                  color: props.theme === 'dark' ? '#f3f4f6' : '#111827'
                }}>
                  正在思考...
                </div>
              </div>
            </Show>
          </div>

          {/* 快速操作 */}
          <div style={{
            padding: '12px',
            'border-top': '1px solid #e5e7eb',
            display: 'flex',
            'flex-wrap': 'wrap',
            gap: '6px'
          }}>
            <For each={quickActions}>
              {(action) => (
                <button
                  style={{
                    padding: '4px 8px',
                    'font-size': '12px',
                    border: '1px solid #d1d5db',
                    'border-radius': '6px',
                    'background-color': 'transparent',
                    color: props.theme === 'dark' ? '#d1d5db' : '#374151',
                    cursor: 'pointer'
                  }}
                  onClick={action.action}
                >
                  {action.label}
                </button>
              )}
            </For>
          </div>

          {/* 输入区域 */}
          <div style={{
            padding: '16px',
            'border-top': '1px solid #e5e7eb',
            display: 'flex',
            gap: '8px'
          }}>
            <input
              type="text"
              value={inputValue()}
              onInput={(e) => setInputValue(e.target.value)}
              onKeyPress={(e) => {
                if (e.key === 'Enter') {
                  handleSendMessage();
                }
              }}
              placeholder="输入您的问题..."
              style={{
                flex: 1,
                padding: '8px 12px',
                border: '1px solid #d1d5db',
                'border-radius': '6px',
                'font-size': '14px',
                'background-color': props.theme === 'dark' ? '#374151' : 'white',
                color: props.theme === 'dark' ? '#f3f4f6' : '#111827'
              }}
              disabled={!modelReady() || isLoading()}
            />
            <button
              style={{
                padding: '8px 12px',
                'background-color': '#3b82f6',
                color: 'white',
                border: 'none',
                'border-radius': '6px',
                cursor: 'pointer',
                'font-size': '14px'
              }}
              onClick={handleSendMessage}
              disabled={!modelReady() || isLoading() || !inputValue().trim()}
            >
              发送
            </button>
          </div>
        </div>
      </Show>
    </>
  );
}
