{"version": 3, "sources": ["../../solid-js/dist/dev.js"], "sourcesContent": ["let taskIdCounter = 1,\n  isCallbackScheduled = false,\n  isPerformingWork = false,\n  taskQueue = [],\n  currentTask = null,\n  shouldYieldToHost = null,\n  yieldInterval = 5,\n  deadline = 0,\n  maxYieldInterval = 300,\n  maxDeadline = 0,\n  scheduleCallback = null,\n  scheduledCallback = null;\nconst maxSigned31BitInt = 1073741823;\nfunction setupScheduler() {\n  const channel = new MessageChannel(),\n    port = channel.port2;\n  scheduleCallback = () => port.postMessage(null);\n  channel.port1.onmessage = () => {\n    if (scheduledCallback !== null) {\n      const currentTime = performance.now();\n      deadline = currentTime + yieldInterval;\n      maxDeadline = currentTime + maxYieldInterval;\n      try {\n        const hasMoreWork = scheduledCallback(currentTime);\n        if (!hasMoreWork) {\n          scheduledCallback = null;\n        } else port.postMessage(null);\n      } catch (error) {\n        port.postMessage(null);\n        throw error;\n      }\n    }\n  };\n  if (navigator && navigator.scheduling && navigator.scheduling.isInputPending) {\n    const scheduling = navigator.scheduling;\n    shouldYieldToHost = () => {\n      const currentTime = performance.now();\n      if (currentTime >= deadline) {\n        if (scheduling.isInputPending()) {\n          return true;\n        }\n        return currentTime >= maxDeadline;\n      } else {\n        return false;\n      }\n    };\n  } else {\n    shouldYieldToHost = () => performance.now() >= deadline;\n  }\n}\nfunction enqueue(taskQueue, task) {\n  function findIndex() {\n    let m = 0;\n    let n = taskQueue.length - 1;\n    while (m <= n) {\n      const k = n + m >> 1;\n      const cmp = task.expirationTime - taskQueue[k].expirationTime;\n      if (cmp > 0) m = k + 1;else if (cmp < 0) n = k - 1;else return k;\n    }\n    return m;\n  }\n  taskQueue.splice(findIndex(), 0, task);\n}\nfunction requestCallback(fn, options) {\n  if (!scheduleCallback) setupScheduler();\n  let startTime = performance.now(),\n    timeout = maxSigned31BitInt;\n  if (options && options.timeout) timeout = options.timeout;\n  const newTask = {\n    id: taskIdCounter++,\n    fn,\n    startTime,\n    expirationTime: startTime + timeout\n  };\n  enqueue(taskQueue, newTask);\n  if (!isCallbackScheduled && !isPerformingWork) {\n    isCallbackScheduled = true;\n    scheduledCallback = flushWork;\n    scheduleCallback();\n  }\n  return newTask;\n}\nfunction cancelCallback(task) {\n  task.fn = null;\n}\nfunction flushWork(initialTime) {\n  isCallbackScheduled = false;\n  isPerformingWork = true;\n  try {\n    return workLoop(initialTime);\n  } finally {\n    currentTask = null;\n    isPerformingWork = false;\n  }\n}\nfunction workLoop(initialTime) {\n  let currentTime = initialTime;\n  currentTask = taskQueue[0] || null;\n  while (currentTask !== null) {\n    if (currentTask.expirationTime > currentTime && shouldYieldToHost()) {\n      break;\n    }\n    const callback = currentTask.fn;\n    if (callback !== null) {\n      currentTask.fn = null;\n      const didUserCallbackTimeout = currentTask.expirationTime <= currentTime;\n      callback(didUserCallbackTimeout);\n      currentTime = performance.now();\n      if (currentTask === taskQueue[0]) {\n        taskQueue.shift();\n      }\n    } else taskQueue.shift();\n    currentTask = taskQueue[0] || null;\n  }\n  return currentTask !== null;\n}\n\nconst sharedConfig = {\n  context: undefined,\n  registry: undefined,\n  effects: undefined,\n  done: false,\n  getContextId() {\n    return getContextId(this.context.count);\n  },\n  getNextContextId() {\n    return getContextId(this.context.count++);\n  }\n};\nfunction getContextId(count) {\n  const num = String(count),\n    len = num.length - 1;\n  return sharedConfig.context.id + (len ? String.fromCharCode(96 + len) : \"\") + num;\n}\nfunction setHydrateContext(context) {\n  sharedConfig.context = context;\n}\nfunction nextHydrateContext() {\n  return {\n    ...sharedConfig.context,\n    id: sharedConfig.getNextContextId(),\n    count: 0\n  };\n}\n\nconst IS_DEV = true;\nconst equalFn = (a, b) => a === b;\nconst $PROXY = Symbol(\"solid-proxy\");\nconst SUPPORTS_PROXY = typeof Proxy === \"function\";\nconst $TRACK = Symbol(\"solid-track\");\nconst $DEVCOMP = Symbol(\"solid-dev-component\");\nconst signalOptions = {\n  equals: equalFn\n};\nlet ERROR = null;\nlet runEffects = runQueue;\nconst STALE = 1;\nconst PENDING = 2;\nconst UNOWNED = {\n  owned: null,\n  cleanups: null,\n  context: null,\n  owner: null\n};\nconst NO_INIT = {};\nvar Owner = null;\nlet Transition = null;\nlet Scheduler = null;\nlet ExternalSourceConfig = null;\nlet Listener = null;\nlet Updates = null;\nlet Effects = null;\nlet ExecCount = 0;\nconst DevHooks = {\n  afterUpdate: null,\n  afterCreateOwner: null,\n  afterCreateSignal: null,\n  afterRegisterGraph: null\n};\nfunction createRoot(fn, detachedOwner) {\n  const listener = Listener,\n    owner = Owner,\n    unowned = fn.length === 0,\n    current = detachedOwner === undefined ? owner : detachedOwner,\n    root = unowned ? {\n      owned: null,\n      cleanups: null,\n      context: null,\n      owner: null\n    }  : {\n      owned: null,\n      cleanups: null,\n      context: current ? current.context : null,\n      owner: current\n    },\n    updateFn = unowned ? () => fn(() => {\n      throw new Error(\"Dispose method must be an explicit argument to createRoot function\");\n    })  : () => fn(() => untrack(() => cleanNode(root)));\n  DevHooks.afterCreateOwner && DevHooks.afterCreateOwner(root);\n  Owner = root;\n  Listener = null;\n  try {\n    return runUpdates(updateFn, true);\n  } finally {\n    Listener = listener;\n    Owner = owner;\n  }\n}\nfunction createSignal(value, options) {\n  options = options ? Object.assign({}, signalOptions, options) : signalOptions;\n  const s = {\n    value,\n    observers: null,\n    observerSlots: null,\n    comparator: options.equals || undefined\n  };\n  {\n    if (options.name) s.name = options.name;\n    if (options.internal) {\n      s.internal = true;\n    } else {\n      registerGraph(s);\n      if (DevHooks.afterCreateSignal) DevHooks.afterCreateSignal(s);\n    }\n  }\n  const setter = value => {\n    if (typeof value === \"function\") {\n      if (Transition && Transition.running && Transition.sources.has(s)) value = value(s.tValue);else value = value(s.value);\n    }\n    return writeSignal(s, value);\n  };\n  return [readSignal.bind(s), setter];\n}\nfunction createComputed(fn, value, options) {\n  const c = createComputation(fn, value, true, STALE, options );\n  if (Scheduler && Transition && Transition.running) Updates.push(c);else updateComputation(c);\n}\nfunction createRenderEffect(fn, value, options) {\n  const c = createComputation(fn, value, false, STALE, options );\n  if (Scheduler && Transition && Transition.running) Updates.push(c);else updateComputation(c);\n}\nfunction createEffect(fn, value, options) {\n  runEffects = runUserEffects;\n  const c = createComputation(fn, value, false, STALE, options ),\n    s = SuspenseContext && useContext(SuspenseContext);\n  if (s) c.suspense = s;\n  if (!options || !options.render) c.user = true;\n  Effects ? Effects.push(c) : updateComputation(c);\n}\nfunction createReaction(onInvalidate, options) {\n  let fn;\n  const c = createComputation(() => {\n      fn ? fn() : untrack(onInvalidate);\n      fn = undefined;\n    }, undefined, false, 0, options ),\n    s = SuspenseContext && useContext(SuspenseContext);\n  if (s) c.suspense = s;\n  c.user = true;\n  return tracking => {\n    fn = tracking;\n    updateComputation(c);\n  };\n}\nfunction createMemo(fn, value, options) {\n  options = options ? Object.assign({}, signalOptions, options) : signalOptions;\n  const c = createComputation(fn, value, true, 0, options );\n  c.observers = null;\n  c.observerSlots = null;\n  c.comparator = options.equals || undefined;\n  if (Scheduler && Transition && Transition.running) {\n    c.tState = STALE;\n    Updates.push(c);\n  } else updateComputation(c);\n  return readSignal.bind(c);\n}\nfunction isPromise(v) {\n  return v && typeof v === \"object\" && \"then\" in v;\n}\nfunction createResource(pSource, pFetcher, pOptions) {\n  let source;\n  let fetcher;\n  let options;\n  if (typeof pFetcher === \"function\") {\n    source = pSource;\n    fetcher = pFetcher;\n    options = pOptions || {};\n  } else {\n    source = true;\n    fetcher = pSource;\n    options = pFetcher || {};\n  }\n  let pr = null,\n    initP = NO_INIT,\n    id = null,\n    loadedUnderTransition = false,\n    scheduled = false,\n    resolved = \"initialValue\" in options,\n    dynamic = typeof source === \"function\" && createMemo(source);\n  const contexts = new Set(),\n    [value, setValue] = (options.storage || createSignal)(options.initialValue),\n    [error, setError] = createSignal(undefined),\n    [track, trigger] = createSignal(undefined, {\n      equals: false\n    }),\n    [state, setState] = createSignal(resolved ? \"ready\" : \"unresolved\");\n  if (sharedConfig.context) {\n    id = sharedConfig.getNextContextId();\n    if (options.ssrLoadFrom === \"initial\") initP = options.initialValue;else if (sharedConfig.load && sharedConfig.has(id)) initP = sharedConfig.load(id);\n  }\n  function loadEnd(p, v, error, key) {\n    if (pr === p) {\n      pr = null;\n      key !== undefined && (resolved = true);\n      if ((p === initP || v === initP) && options.onHydrated) queueMicrotask(() => options.onHydrated(key, {\n        value: v\n      }));\n      initP = NO_INIT;\n      if (Transition && p && loadedUnderTransition) {\n        Transition.promises.delete(p);\n        loadedUnderTransition = false;\n        runUpdates(() => {\n          Transition.running = true;\n          completeLoad(v, error);\n        }, false);\n      } else completeLoad(v, error);\n    }\n    return v;\n  }\n  function completeLoad(v, err) {\n    runUpdates(() => {\n      if (err === undefined) setValue(() => v);\n      setState(err !== undefined ? \"errored\" : resolved ? \"ready\" : \"unresolved\");\n      setError(err);\n      for (const c of contexts.keys()) c.decrement();\n      contexts.clear();\n    }, false);\n  }\n  function read() {\n    const c = SuspenseContext && useContext(SuspenseContext),\n      v = value(),\n      err = error();\n    if (err !== undefined && !pr) throw err;\n    if (Listener && !Listener.user && c) {\n      createComputed(() => {\n        track();\n        if (pr) {\n          if (c.resolved && Transition && loadedUnderTransition) Transition.promises.add(pr);else if (!contexts.has(c)) {\n            c.increment();\n            contexts.add(c);\n          }\n        }\n      });\n    }\n    return v;\n  }\n  function load(refetching = true) {\n    if (refetching !== false && scheduled) return;\n    scheduled = false;\n    const lookup = dynamic ? dynamic() : source;\n    loadedUnderTransition = Transition && Transition.running;\n    if (lookup == null || lookup === false) {\n      loadEnd(pr, untrack(value));\n      return;\n    }\n    if (Transition && pr) Transition.promises.delete(pr);\n    let error;\n    const p = initP !== NO_INIT ? initP : untrack(() => {\n      try {\n        return fetcher(lookup, {\n          value: value(),\n          refetching\n        });\n      } catch (fetcherError) {\n        error = fetcherError;\n      }\n    });\n    if (error !== undefined) {\n      loadEnd(pr, undefined, castError(error), lookup);\n      return;\n    } else if (!isPromise(p)) {\n      loadEnd(pr, p, undefined, lookup);\n      return p;\n    }\n    pr = p;\n    if (\"v\" in p) {\n      if (p.s === 1) loadEnd(pr, p.v, undefined, lookup);else loadEnd(pr, undefined, castError(p.v), lookup);\n      return p;\n    }\n    scheduled = true;\n    queueMicrotask(() => scheduled = false);\n    runUpdates(() => {\n      setState(resolved ? \"refreshing\" : \"pending\");\n      trigger();\n    }, false);\n    return p.then(v => loadEnd(p, v, undefined, lookup), e => loadEnd(p, undefined, castError(e), lookup));\n  }\n  Object.defineProperties(read, {\n    state: {\n      get: () => state()\n    },\n    error: {\n      get: () => error()\n    },\n    loading: {\n      get() {\n        const s = state();\n        return s === \"pending\" || s === \"refreshing\";\n      }\n    },\n    latest: {\n      get() {\n        if (!resolved) return read();\n        const err = error();\n        if (err && !pr) throw err;\n        return value();\n      }\n    }\n  });\n  let owner = Owner;\n  if (dynamic) createComputed(() => (owner = Owner, load(false)));else load(false);\n  return [read, {\n    refetch: info => runWithOwner(owner, () => load(info)),\n    mutate: setValue\n  }];\n}\nfunction createDeferred(source, options) {\n  let t,\n    timeout = options ? options.timeoutMs : undefined;\n  const node = createComputation(() => {\n    if (!t || !t.fn) t = requestCallback(() => setDeferred(() => node.value), timeout !== undefined ? {\n      timeout\n    } : undefined);\n    return source();\n  }, undefined, true);\n  const [deferred, setDeferred] = createSignal(Transition && Transition.running && Transition.sources.has(node) ? node.tValue : node.value, options);\n  updateComputation(node);\n  setDeferred(() => Transition && Transition.running && Transition.sources.has(node) ? node.tValue : node.value);\n  return deferred;\n}\nfunction createSelector(source, fn = equalFn, options) {\n  const subs = new Map();\n  const node = createComputation(p => {\n    const v = source();\n    for (const [key, val] of subs.entries()) if (fn(key, v) !== fn(key, p)) {\n      for (const c of val.values()) {\n        c.state = STALE;\n        if (c.pure) Updates.push(c);else Effects.push(c);\n      }\n    }\n    return v;\n  }, undefined, true, STALE, options );\n  updateComputation(node);\n  return key => {\n    const listener = Listener;\n    if (listener) {\n      let l;\n      if (l = subs.get(key)) l.add(listener);else subs.set(key, l = new Set([listener]));\n      onCleanup(() => {\n        l.delete(listener);\n        !l.size && subs.delete(key);\n      });\n    }\n    return fn(key, Transition && Transition.running && Transition.sources.has(node) ? node.tValue : node.value);\n  };\n}\nfunction batch(fn) {\n  return runUpdates(fn, false);\n}\nfunction untrack(fn) {\n  if (!ExternalSourceConfig && Listener === null) return fn();\n  const listener = Listener;\n  Listener = null;\n  try {\n    if (ExternalSourceConfig) return ExternalSourceConfig.untrack(fn);\n    return fn();\n  } finally {\n    Listener = listener;\n  }\n}\nfunction on(deps, fn, options) {\n  const isArray = Array.isArray(deps);\n  let prevInput;\n  let defer = options && options.defer;\n  return prevValue => {\n    let input;\n    if (isArray) {\n      input = Array(deps.length);\n      for (let i = 0; i < deps.length; i++) input[i] = deps[i]();\n    } else input = deps();\n    if (defer) {\n      defer = false;\n      return prevValue;\n    }\n    const result = untrack(() => fn(input, prevInput, prevValue));\n    prevInput = input;\n    return result;\n  };\n}\nfunction onMount(fn) {\n  createEffect(() => untrack(fn));\n}\nfunction onCleanup(fn) {\n  if (Owner === null) console.warn(\"cleanups created outside a `createRoot` or `render` will never be run\");else if (Owner.cleanups === null) Owner.cleanups = [fn];else Owner.cleanups.push(fn);\n  return fn;\n}\nfunction catchError(fn, handler) {\n  ERROR || (ERROR = Symbol(\"error\"));\n  Owner = createComputation(undefined, undefined, true);\n  Owner.context = {\n    ...Owner.context,\n    [ERROR]: [handler]\n  };\n  if (Transition && Transition.running) Transition.sources.add(Owner);\n  try {\n    return fn();\n  } catch (err) {\n    handleError(err);\n  } finally {\n    Owner = Owner.owner;\n  }\n}\nfunction getListener() {\n  return Listener;\n}\nfunction getOwner() {\n  return Owner;\n}\nfunction runWithOwner(o, fn) {\n  const prev = Owner;\n  const prevListener = Listener;\n  Owner = o;\n  Listener = null;\n  try {\n    return runUpdates(fn, true);\n  } catch (err) {\n    handleError(err);\n  } finally {\n    Owner = prev;\n    Listener = prevListener;\n  }\n}\nfunction enableScheduling(scheduler = requestCallback) {\n  Scheduler = scheduler;\n}\nfunction startTransition(fn) {\n  if (Transition && Transition.running) {\n    fn();\n    return Transition.done;\n  }\n  const l = Listener;\n  const o = Owner;\n  return Promise.resolve().then(() => {\n    Listener = l;\n    Owner = o;\n    let t;\n    if (Scheduler || SuspenseContext) {\n      t = Transition || (Transition = {\n        sources: new Set(),\n        effects: [],\n        promises: new Set(),\n        disposed: new Set(),\n        queue: new Set(),\n        running: true\n      });\n      t.done || (t.done = new Promise(res => t.resolve = res));\n      t.running = true;\n    }\n    runUpdates(fn, false);\n    Listener = Owner = null;\n    return t ? t.done : undefined;\n  });\n}\nconst [transPending, setTransPending] = /*@__PURE__*/createSignal(false);\nfunction useTransition() {\n  return [transPending, startTransition];\n}\nfunction resumeEffects(e) {\n  Effects.push.apply(Effects, e);\n  e.length = 0;\n}\nfunction devComponent(Comp, props) {\n  const c = createComputation(() => untrack(() => {\n    Object.assign(Comp, {\n      [$DEVCOMP]: true\n    });\n    return Comp(props);\n  }), undefined, true, 0);\n  c.props = props;\n  c.observers = null;\n  c.observerSlots = null;\n  c.name = Comp.name;\n  c.component = Comp;\n  updateComputation(c);\n  return c.tValue !== undefined ? c.tValue : c.value;\n}\nfunction registerGraph(value) {\n  if (Owner) {\n    if (Owner.sourceMap) Owner.sourceMap.push(value);else Owner.sourceMap = [value];\n    value.graph = Owner;\n  }\n  if (DevHooks.afterRegisterGraph) DevHooks.afterRegisterGraph(value);\n}\nfunction createContext(defaultValue, options) {\n  const id = Symbol(\"context\");\n  return {\n    id,\n    Provider: createProvider(id, options),\n    defaultValue\n  };\n}\nfunction useContext(context) {\n  let value;\n  return Owner && Owner.context && (value = Owner.context[context.id]) !== undefined ? value : context.defaultValue;\n}\nfunction children(fn) {\n  const children = createMemo(fn);\n  const memo = createMemo(() => resolveChildren(children()), undefined, {\n    name: \"children\"\n  }) ;\n  memo.toArray = () => {\n    const c = memo();\n    return Array.isArray(c) ? c : c != null ? [c] : [];\n  };\n  return memo;\n}\nlet SuspenseContext;\nfunction getSuspenseContext() {\n  return SuspenseContext || (SuspenseContext = createContext());\n}\nfunction enableExternalSource(factory, untrack = fn => fn()) {\n  if (ExternalSourceConfig) {\n    const {\n      factory: oldFactory,\n      untrack: oldUntrack\n    } = ExternalSourceConfig;\n    ExternalSourceConfig = {\n      factory: (fn, trigger) => {\n        const oldSource = oldFactory(fn, trigger);\n        const source = factory(x => oldSource.track(x), trigger);\n        return {\n          track: x => source.track(x),\n          dispose() {\n            source.dispose();\n            oldSource.dispose();\n          }\n        };\n      },\n      untrack: fn => oldUntrack(() => untrack(fn))\n    };\n  } else {\n    ExternalSourceConfig = {\n      factory,\n      untrack\n    };\n  }\n}\nfunction readSignal() {\n  const runningTransition = Transition && Transition.running;\n  if (this.sources && (runningTransition ? this.tState : this.state)) {\n    if ((runningTransition ? this.tState : this.state) === STALE) updateComputation(this);else {\n      const updates = Updates;\n      Updates = null;\n      runUpdates(() => lookUpstream(this), false);\n      Updates = updates;\n    }\n  }\n  if (Listener) {\n    const sSlot = this.observers ? this.observers.length : 0;\n    if (!Listener.sources) {\n      Listener.sources = [this];\n      Listener.sourceSlots = [sSlot];\n    } else {\n      Listener.sources.push(this);\n      Listener.sourceSlots.push(sSlot);\n    }\n    if (!this.observers) {\n      this.observers = [Listener];\n      this.observerSlots = [Listener.sources.length - 1];\n    } else {\n      this.observers.push(Listener);\n      this.observerSlots.push(Listener.sources.length - 1);\n    }\n  }\n  if (runningTransition && Transition.sources.has(this)) return this.tValue;\n  return this.value;\n}\nfunction writeSignal(node, value, isComp) {\n  let current = Transition && Transition.running && Transition.sources.has(node) ? node.tValue : node.value;\n  if (!node.comparator || !node.comparator(current, value)) {\n    if (Transition) {\n      const TransitionRunning = Transition.running;\n      if (TransitionRunning || !isComp && Transition.sources.has(node)) {\n        Transition.sources.add(node);\n        node.tValue = value;\n      }\n      if (!TransitionRunning) node.value = value;\n    } else node.value = value;\n    if (node.observers && node.observers.length) {\n      runUpdates(() => {\n        for (let i = 0; i < node.observers.length; i += 1) {\n          const o = node.observers[i];\n          const TransitionRunning = Transition && Transition.running;\n          if (TransitionRunning && Transition.disposed.has(o)) continue;\n          if (TransitionRunning ? !o.tState : !o.state) {\n            if (o.pure) Updates.push(o);else Effects.push(o);\n            if (o.observers) markDownstream(o);\n          }\n          if (!TransitionRunning) o.state = STALE;else o.tState = STALE;\n        }\n        if (Updates.length > 10e5) {\n          Updates = [];\n          if (IS_DEV) throw new Error(\"Potential Infinite Loop Detected.\");\n          throw new Error();\n        }\n      }, false);\n    }\n  }\n  return value;\n}\nfunction updateComputation(node) {\n  if (!node.fn) return;\n  cleanNode(node);\n  const time = ExecCount;\n  runComputation(node, Transition && Transition.running && Transition.sources.has(node) ? node.tValue : node.value, time);\n  if (Transition && !Transition.running && Transition.sources.has(node)) {\n    queueMicrotask(() => {\n      runUpdates(() => {\n        Transition && (Transition.running = true);\n        Listener = Owner = node;\n        runComputation(node, node.tValue, time);\n        Listener = Owner = null;\n      }, false);\n    });\n  }\n}\nfunction runComputation(node, value, time) {\n  let nextValue;\n  const owner = Owner,\n    listener = Listener;\n  Listener = Owner = node;\n  try {\n    nextValue = node.fn(value);\n  } catch (err) {\n    if (node.pure) {\n      if (Transition && Transition.running) {\n        node.tState = STALE;\n        node.tOwned && node.tOwned.forEach(cleanNode);\n        node.tOwned = undefined;\n      } else {\n        node.state = STALE;\n        node.owned && node.owned.forEach(cleanNode);\n        node.owned = null;\n      }\n    }\n    node.updatedAt = time + 1;\n    return handleError(err);\n  } finally {\n    Listener = listener;\n    Owner = owner;\n  }\n  if (!node.updatedAt || node.updatedAt <= time) {\n    if (node.updatedAt != null && \"observers\" in node) {\n      writeSignal(node, nextValue, true);\n    } else if (Transition && Transition.running && node.pure) {\n      Transition.sources.add(node);\n      node.tValue = nextValue;\n    } else node.value = nextValue;\n    node.updatedAt = time;\n  }\n}\nfunction createComputation(fn, init, pure, state = STALE, options) {\n  const c = {\n    fn,\n    state: state,\n    updatedAt: null,\n    owned: null,\n    sources: null,\n    sourceSlots: null,\n    cleanups: null,\n    value: init,\n    owner: Owner,\n    context: Owner ? Owner.context : null,\n    pure\n  };\n  if (Transition && Transition.running) {\n    c.state = 0;\n    c.tState = state;\n  }\n  if (Owner === null) console.warn(\"computations created outside a `createRoot` or `render` will never be disposed\");else if (Owner !== UNOWNED) {\n    if (Transition && Transition.running && Owner.pure) {\n      if (!Owner.tOwned) Owner.tOwned = [c];else Owner.tOwned.push(c);\n    } else {\n      if (!Owner.owned) Owner.owned = [c];else Owner.owned.push(c);\n    }\n  }\n  if (options && options.name) c.name = options.name;\n  if (ExternalSourceConfig && c.fn) {\n    const [track, trigger] = createSignal(undefined, {\n      equals: false\n    });\n    const ordinary = ExternalSourceConfig.factory(c.fn, trigger);\n    onCleanup(() => ordinary.dispose());\n    const triggerInTransition = () => startTransition(trigger).then(() => inTransition.dispose());\n    const inTransition = ExternalSourceConfig.factory(c.fn, triggerInTransition);\n    c.fn = x => {\n      track();\n      return Transition && Transition.running ? inTransition.track(x) : ordinary.track(x);\n    };\n  }\n  DevHooks.afterCreateOwner && DevHooks.afterCreateOwner(c);\n  return c;\n}\nfunction runTop(node) {\n  const runningTransition = Transition && Transition.running;\n  if ((runningTransition ? node.tState : node.state) === 0) return;\n  if ((runningTransition ? node.tState : node.state) === PENDING) return lookUpstream(node);\n  if (node.suspense && untrack(node.suspense.inFallback)) return node.suspense.effects.push(node);\n  const ancestors = [node];\n  while ((node = node.owner) && (!node.updatedAt || node.updatedAt < ExecCount)) {\n    if (runningTransition && Transition.disposed.has(node)) return;\n    if (runningTransition ? node.tState : node.state) ancestors.push(node);\n  }\n  for (let i = ancestors.length - 1; i >= 0; i--) {\n    node = ancestors[i];\n    if (runningTransition) {\n      let top = node,\n        prev = ancestors[i + 1];\n      while ((top = top.owner) && top !== prev) {\n        if (Transition.disposed.has(top)) return;\n      }\n    }\n    if ((runningTransition ? node.tState : node.state) === STALE) {\n      updateComputation(node);\n    } else if ((runningTransition ? node.tState : node.state) === PENDING) {\n      const updates = Updates;\n      Updates = null;\n      runUpdates(() => lookUpstream(node, ancestors[0]), false);\n      Updates = updates;\n    }\n  }\n}\nfunction runUpdates(fn, init) {\n  if (Updates) return fn();\n  let wait = false;\n  if (!init) Updates = [];\n  if (Effects) wait = true;else Effects = [];\n  ExecCount++;\n  try {\n    const res = fn();\n    completeUpdates(wait);\n    return res;\n  } catch (err) {\n    if (!wait) Effects = null;\n    Updates = null;\n    handleError(err);\n  }\n}\nfunction completeUpdates(wait) {\n  if (Updates) {\n    if (Scheduler && Transition && Transition.running) scheduleQueue(Updates);else runQueue(Updates);\n    Updates = null;\n  }\n  if (wait) return;\n  let res;\n  if (Transition) {\n    if (!Transition.promises.size && !Transition.queue.size) {\n      const sources = Transition.sources;\n      const disposed = Transition.disposed;\n      Effects.push.apply(Effects, Transition.effects);\n      res = Transition.resolve;\n      for (const e of Effects) {\n        \"tState\" in e && (e.state = e.tState);\n        delete e.tState;\n      }\n      Transition = null;\n      runUpdates(() => {\n        for (const d of disposed) cleanNode(d);\n        for (const v of sources) {\n          v.value = v.tValue;\n          if (v.owned) {\n            for (let i = 0, len = v.owned.length; i < len; i++) cleanNode(v.owned[i]);\n          }\n          if (v.tOwned) v.owned = v.tOwned;\n          delete v.tValue;\n          delete v.tOwned;\n          v.tState = 0;\n        }\n        setTransPending(false);\n      }, false);\n    } else if (Transition.running) {\n      Transition.running = false;\n      Transition.effects.push.apply(Transition.effects, Effects);\n      Effects = null;\n      setTransPending(true);\n      return;\n    }\n  }\n  const e = Effects;\n  Effects = null;\n  if (e.length) runUpdates(() => runEffects(e), false);else DevHooks.afterUpdate && DevHooks.afterUpdate();\n  if (res) res();\n}\nfunction runQueue(queue) {\n  for (let i = 0; i < queue.length; i++) runTop(queue[i]);\n}\nfunction scheduleQueue(queue) {\n  for (let i = 0; i < queue.length; i++) {\n    const item = queue[i];\n    const tasks = Transition.queue;\n    if (!tasks.has(item)) {\n      tasks.add(item);\n      Scheduler(() => {\n        tasks.delete(item);\n        runUpdates(() => {\n          Transition.running = true;\n          runTop(item);\n        }, false);\n        Transition && (Transition.running = false);\n      });\n    }\n  }\n}\nfunction runUserEffects(queue) {\n  let i,\n    userLength = 0;\n  for (i = 0; i < queue.length; i++) {\n    const e = queue[i];\n    if (!e.user) runTop(e);else queue[userLength++] = e;\n  }\n  if (sharedConfig.context) {\n    if (sharedConfig.count) {\n      sharedConfig.effects || (sharedConfig.effects = []);\n      sharedConfig.effects.push(...queue.slice(0, userLength));\n      return;\n    }\n    setHydrateContext();\n  }\n  if (sharedConfig.effects && (sharedConfig.done || !sharedConfig.count)) {\n    queue = [...sharedConfig.effects, ...queue];\n    userLength += sharedConfig.effects.length;\n    delete sharedConfig.effects;\n  }\n  for (i = 0; i < userLength; i++) runTop(queue[i]);\n}\nfunction lookUpstream(node, ignore) {\n  const runningTransition = Transition && Transition.running;\n  if (runningTransition) node.tState = 0;else node.state = 0;\n  for (let i = 0; i < node.sources.length; i += 1) {\n    const source = node.sources[i];\n    if (source.sources) {\n      const state = runningTransition ? source.tState : source.state;\n      if (state === STALE) {\n        if (source !== ignore && (!source.updatedAt || source.updatedAt < ExecCount)) runTop(source);\n      } else if (state === PENDING) lookUpstream(source, ignore);\n    }\n  }\n}\nfunction markDownstream(node) {\n  const runningTransition = Transition && Transition.running;\n  for (let i = 0; i < node.observers.length; i += 1) {\n    const o = node.observers[i];\n    if (runningTransition ? !o.tState : !o.state) {\n      if (runningTransition) o.tState = PENDING;else o.state = PENDING;\n      if (o.pure) Updates.push(o);else Effects.push(o);\n      o.observers && markDownstream(o);\n    }\n  }\n}\nfunction cleanNode(node) {\n  let i;\n  if (node.sources) {\n    while (node.sources.length) {\n      const source = node.sources.pop(),\n        index = node.sourceSlots.pop(),\n        obs = source.observers;\n      if (obs && obs.length) {\n        const n = obs.pop(),\n          s = source.observerSlots.pop();\n        if (index < obs.length) {\n          n.sourceSlots[s] = index;\n          obs[index] = n;\n          source.observerSlots[index] = s;\n        }\n      }\n    }\n  }\n  if (node.tOwned) {\n    for (i = node.tOwned.length - 1; i >= 0; i--) cleanNode(node.tOwned[i]);\n    delete node.tOwned;\n  }\n  if (Transition && Transition.running && node.pure) {\n    reset(node, true);\n  } else if (node.owned) {\n    for (i = node.owned.length - 1; i >= 0; i--) cleanNode(node.owned[i]);\n    node.owned = null;\n  }\n  if (node.cleanups) {\n    for (i = node.cleanups.length - 1; i >= 0; i--) node.cleanups[i]();\n    node.cleanups = null;\n  }\n  if (Transition && Transition.running) node.tState = 0;else node.state = 0;\n  delete node.sourceMap;\n}\nfunction reset(node, top) {\n  if (!top) {\n    node.tState = 0;\n    Transition.disposed.add(node);\n  }\n  if (node.owned) {\n    for (let i = 0; i < node.owned.length; i++) reset(node.owned[i]);\n  }\n}\nfunction castError(err) {\n  if (err instanceof Error) return err;\n  return new Error(typeof err === \"string\" ? err : \"Unknown error\", {\n    cause: err\n  });\n}\nfunction runErrors(err, fns, owner) {\n  try {\n    for (const f of fns) f(err);\n  } catch (e) {\n    handleError(e, owner && owner.owner || null);\n  }\n}\nfunction handleError(err, owner = Owner) {\n  const fns = ERROR && owner && owner.context && owner.context[ERROR];\n  const error = castError(err);\n  if (!fns) throw error;\n  if (Effects) Effects.push({\n    fn() {\n      runErrors(error, fns, owner);\n    },\n    state: STALE\n  });else runErrors(error, fns, owner);\n}\nfunction resolveChildren(children) {\n  if (typeof children === \"function\" && !children.length) return resolveChildren(children());\n  if (Array.isArray(children)) {\n    const results = [];\n    for (let i = 0; i < children.length; i++) {\n      const result = resolveChildren(children[i]);\n      Array.isArray(result) ? results.push.apply(results, result) : results.push(result);\n    }\n    return results;\n  }\n  return children;\n}\nfunction createProvider(id, options) {\n  return function provider(props) {\n    let res;\n    createRenderEffect(() => res = untrack(() => {\n      Owner.context = {\n        ...Owner.context,\n        [id]: props.value\n      };\n      return children(() => props.children);\n    }), undefined, options);\n    return res;\n  };\n}\nfunction onError(fn) {\n  ERROR || (ERROR = Symbol(\"error\"));\n  if (Owner === null) console.warn(\"error handlers created outside a `createRoot` or `render` will never be run\");else if (Owner.context === null || !Owner.context[ERROR]) {\n    Owner.context = {\n      ...Owner.context,\n      [ERROR]: [fn]\n    };\n    mutateContext(Owner, ERROR, [fn]);\n  } else Owner.context[ERROR].push(fn);\n}\nfunction mutateContext(o, key, value) {\n  if (o.owned) {\n    for (let i = 0; i < o.owned.length; i++) {\n      if (o.owned[i].context === o.context) mutateContext(o.owned[i], key, value);\n      if (!o.owned[i].context) {\n        o.owned[i].context = o.context;\n        mutateContext(o.owned[i], key, value);\n      } else if (!o.owned[i].context[key]) {\n        o.owned[i].context[key] = value;\n        mutateContext(o.owned[i], key, value);\n      }\n    }\n  }\n}\n\nfunction observable(input) {\n  return {\n    subscribe(observer) {\n      if (!(observer instanceof Object) || observer == null) {\n        throw new TypeError(\"Expected the observer to be an object.\");\n      }\n      const handler = typeof observer === \"function\" ? observer : observer.next && observer.next.bind(observer);\n      if (!handler) {\n        return {\n          unsubscribe() {}\n        };\n      }\n      const dispose = createRoot(disposer => {\n        createEffect(() => {\n          const v = input();\n          untrack(() => handler(v));\n        });\n        return disposer;\n      });\n      if (getOwner()) onCleanup(dispose);\n      return {\n        unsubscribe() {\n          dispose();\n        }\n      };\n    },\n    [Symbol.observable || \"@@observable\"]() {\n      return this;\n    }\n  };\n}\nfunction from(producer, initalValue = undefined) {\n  const [s, set] = createSignal(initalValue, {\n    equals: false\n  });\n  if (\"subscribe\" in producer) {\n    const unsub = producer.subscribe(v => set(() => v));\n    onCleanup(() => \"unsubscribe\" in unsub ? unsub.unsubscribe() : unsub());\n  } else {\n    const clean = producer(set);\n    onCleanup(clean);\n  }\n  return s;\n}\n\nconst FALLBACK = Symbol(\"fallback\");\nfunction dispose(d) {\n  for (let i = 0; i < d.length; i++) d[i]();\n}\nfunction mapArray(list, mapFn, options = {}) {\n  let items = [],\n    mapped = [],\n    disposers = [],\n    len = 0,\n    indexes = mapFn.length > 1 ? [] : null;\n  onCleanup(() => dispose(disposers));\n  return () => {\n    let newItems = list() || [],\n      newLen = newItems.length,\n      i,\n      j;\n    newItems[$TRACK];\n    return untrack(() => {\n      let newIndices, newIndicesNext, temp, tempdisposers, tempIndexes, start, end, newEnd, item;\n      if (newLen === 0) {\n        if (len !== 0) {\n          dispose(disposers);\n          disposers = [];\n          items = [];\n          mapped = [];\n          len = 0;\n          indexes && (indexes = []);\n        }\n        if (options.fallback) {\n          items = [FALLBACK];\n          mapped[0] = createRoot(disposer => {\n            disposers[0] = disposer;\n            return options.fallback();\n          });\n          len = 1;\n        }\n      }\n      else if (len === 0) {\n        mapped = new Array(newLen);\n        for (j = 0; j < newLen; j++) {\n          items[j] = newItems[j];\n          mapped[j] = createRoot(mapper);\n        }\n        len = newLen;\n      } else {\n        temp = new Array(newLen);\n        tempdisposers = new Array(newLen);\n        indexes && (tempIndexes = new Array(newLen));\n        for (start = 0, end = Math.min(len, newLen); start < end && items[start] === newItems[start]; start++);\n        for (end = len - 1, newEnd = newLen - 1; end >= start && newEnd >= start && items[end] === newItems[newEnd]; end--, newEnd--) {\n          temp[newEnd] = mapped[end];\n          tempdisposers[newEnd] = disposers[end];\n          indexes && (tempIndexes[newEnd] = indexes[end]);\n        }\n        newIndices = new Map();\n        newIndicesNext = new Array(newEnd + 1);\n        for (j = newEnd; j >= start; j--) {\n          item = newItems[j];\n          i = newIndices.get(item);\n          newIndicesNext[j] = i === undefined ? -1 : i;\n          newIndices.set(item, j);\n        }\n        for (i = start; i <= end; i++) {\n          item = items[i];\n          j = newIndices.get(item);\n          if (j !== undefined && j !== -1) {\n            temp[j] = mapped[i];\n            tempdisposers[j] = disposers[i];\n            indexes && (tempIndexes[j] = indexes[i]);\n            j = newIndicesNext[j];\n            newIndices.set(item, j);\n          } else disposers[i]();\n        }\n        for (j = start; j < newLen; j++) {\n          if (j in temp) {\n            mapped[j] = temp[j];\n            disposers[j] = tempdisposers[j];\n            if (indexes) {\n              indexes[j] = tempIndexes[j];\n              indexes[j](j);\n            }\n          } else mapped[j] = createRoot(mapper);\n        }\n        mapped = mapped.slice(0, len = newLen);\n        items = newItems.slice(0);\n      }\n      return mapped;\n    });\n    function mapper(disposer) {\n      disposers[j] = disposer;\n      if (indexes) {\n        const [s, set] = createSignal(j, {\n          name: \"index\"\n        }) ;\n        indexes[j] = set;\n        return mapFn(newItems[j], s);\n      }\n      return mapFn(newItems[j]);\n    }\n  };\n}\nfunction indexArray(list, mapFn, options = {}) {\n  let items = [],\n    mapped = [],\n    disposers = [],\n    signals = [],\n    len = 0,\n    i;\n  onCleanup(() => dispose(disposers));\n  return () => {\n    const newItems = list() || [],\n      newLen = newItems.length;\n    newItems[$TRACK];\n    return untrack(() => {\n      if (newLen === 0) {\n        if (len !== 0) {\n          dispose(disposers);\n          disposers = [];\n          items = [];\n          mapped = [];\n          len = 0;\n          signals = [];\n        }\n        if (options.fallback) {\n          items = [FALLBACK];\n          mapped[0] = createRoot(disposer => {\n            disposers[0] = disposer;\n            return options.fallback();\n          });\n          len = 1;\n        }\n        return mapped;\n      }\n      if (items[0] === FALLBACK) {\n        disposers[0]();\n        disposers = [];\n        items = [];\n        mapped = [];\n        len = 0;\n      }\n      for (i = 0; i < newLen; i++) {\n        if (i < items.length && items[i] !== newItems[i]) {\n          signals[i](() => newItems[i]);\n        } else if (i >= items.length) {\n          mapped[i] = createRoot(mapper);\n        }\n      }\n      for (; i < items.length; i++) {\n        disposers[i]();\n      }\n      len = signals.length = disposers.length = newLen;\n      items = newItems.slice(0);\n      return mapped = mapped.slice(0, len);\n    });\n    function mapper(disposer) {\n      disposers[i] = disposer;\n      const [s, set] = createSignal(newItems[i], {\n        name: \"value\"\n      }) ;\n      signals[i] = set;\n      return mapFn(s, i);\n    }\n  };\n}\n\nlet hydrationEnabled = false;\nfunction enableHydration() {\n  hydrationEnabled = true;\n}\nfunction createComponent(Comp, props) {\n  if (hydrationEnabled) {\n    if (sharedConfig.context) {\n      const c = sharedConfig.context;\n      setHydrateContext(nextHydrateContext());\n      const r = devComponent(Comp, props || {}) ;\n      setHydrateContext(c);\n      return r;\n    }\n  }\n  return devComponent(Comp, props || {});\n}\nfunction trueFn() {\n  return true;\n}\nconst propTraps = {\n  get(_, property, receiver) {\n    if (property === $PROXY) return receiver;\n    return _.get(property);\n  },\n  has(_, property) {\n    if (property === $PROXY) return true;\n    return _.has(property);\n  },\n  set: trueFn,\n  deleteProperty: trueFn,\n  getOwnPropertyDescriptor(_, property) {\n    return {\n      configurable: true,\n      enumerable: true,\n      get() {\n        return _.get(property);\n      },\n      set: trueFn,\n      deleteProperty: trueFn\n    };\n  },\n  ownKeys(_) {\n    return _.keys();\n  }\n};\nfunction resolveSource(s) {\n  return !(s = typeof s === \"function\" ? s() : s) ? {} : s;\n}\nfunction resolveSources() {\n  for (let i = 0, length = this.length; i < length; ++i) {\n    const v = this[i]();\n    if (v !== undefined) return v;\n  }\n}\nfunction mergeProps(...sources) {\n  let proxy = false;\n  for (let i = 0; i < sources.length; i++) {\n    const s = sources[i];\n    proxy = proxy || !!s && $PROXY in s;\n    sources[i] = typeof s === \"function\" ? (proxy = true, createMemo(s)) : s;\n  }\n  if (SUPPORTS_PROXY && proxy) {\n    return new Proxy({\n      get(property) {\n        for (let i = sources.length - 1; i >= 0; i--) {\n          const v = resolveSource(sources[i])[property];\n          if (v !== undefined) return v;\n        }\n      },\n      has(property) {\n        for (let i = sources.length - 1; i >= 0; i--) {\n          if (property in resolveSource(sources[i])) return true;\n        }\n        return false;\n      },\n      keys() {\n        const keys = [];\n        for (let i = 0; i < sources.length; i++) keys.push(...Object.keys(resolveSource(sources[i])));\n        return [...new Set(keys)];\n      }\n    }, propTraps);\n  }\n  const sourcesMap = {};\n  const defined = Object.create(null);\n  for (let i = sources.length - 1; i >= 0; i--) {\n    const source = sources[i];\n    if (!source) continue;\n    const sourceKeys = Object.getOwnPropertyNames(source);\n    for (let i = sourceKeys.length - 1; i >= 0; i--) {\n      const key = sourceKeys[i];\n      if (key === \"__proto__\" || key === \"constructor\") continue;\n      const desc = Object.getOwnPropertyDescriptor(source, key);\n      if (!defined[key]) {\n        defined[key] = desc.get ? {\n          enumerable: true,\n          configurable: true,\n          get: resolveSources.bind(sourcesMap[key] = [desc.get.bind(source)])\n        } : desc.value !== undefined ? desc : undefined;\n      } else {\n        const sources = sourcesMap[key];\n        if (sources) {\n          if (desc.get) sources.push(desc.get.bind(source));else if (desc.value !== undefined) sources.push(() => desc.value);\n        }\n      }\n    }\n  }\n  const target = {};\n  const definedKeys = Object.keys(defined);\n  for (let i = definedKeys.length - 1; i >= 0; i--) {\n    const key = definedKeys[i],\n      desc = defined[key];\n    if (desc && desc.get) Object.defineProperty(target, key, desc);else target[key] = desc ? desc.value : undefined;\n  }\n  return target;\n}\nfunction splitProps(props, ...keys) {\n  if (SUPPORTS_PROXY && $PROXY in props) {\n    const blocked = new Set(keys.length > 1 ? keys.flat() : keys[0]);\n    const res = keys.map(k => {\n      return new Proxy({\n        get(property) {\n          return k.includes(property) ? props[property] : undefined;\n        },\n        has(property) {\n          return k.includes(property) && property in props;\n        },\n        keys() {\n          return k.filter(property => property in props);\n        }\n      }, propTraps);\n    });\n    res.push(new Proxy({\n      get(property) {\n        return blocked.has(property) ? undefined : props[property];\n      },\n      has(property) {\n        return blocked.has(property) ? false : property in props;\n      },\n      keys() {\n        return Object.keys(props).filter(k => !blocked.has(k));\n      }\n    }, propTraps));\n    return res;\n  }\n  const otherObject = {};\n  const objects = keys.map(() => ({}));\n  for (const propName of Object.getOwnPropertyNames(props)) {\n    const desc = Object.getOwnPropertyDescriptor(props, propName);\n    const isDefaultDesc = !desc.get && !desc.set && desc.enumerable && desc.writable && desc.configurable;\n    let blocked = false;\n    let objectIndex = 0;\n    for (const k of keys) {\n      if (k.includes(propName)) {\n        blocked = true;\n        isDefaultDesc ? objects[objectIndex][propName] = desc.value : Object.defineProperty(objects[objectIndex], propName, desc);\n      }\n      ++objectIndex;\n    }\n    if (!blocked) {\n      isDefaultDesc ? otherObject[propName] = desc.value : Object.defineProperty(otherObject, propName, desc);\n    }\n  }\n  return [...objects, otherObject];\n}\nfunction lazy(fn) {\n  let comp;\n  let p;\n  const wrap = props => {\n    const ctx = sharedConfig.context;\n    if (ctx) {\n      const [s, set] = createSignal();\n      sharedConfig.count || (sharedConfig.count = 0);\n      sharedConfig.count++;\n      (p || (p = fn())).then(mod => {\n        !sharedConfig.done && setHydrateContext(ctx);\n        sharedConfig.count--;\n        set(() => mod.default);\n        setHydrateContext();\n      });\n      comp = s;\n    } else if (!comp) {\n      const [s] = createResource(() => (p || (p = fn())).then(mod => mod.default));\n      comp = s;\n    }\n    let Comp;\n    return createMemo(() => (Comp = comp()) ? untrack(() => {\n      if (IS_DEV) Object.assign(Comp, {\n        [$DEVCOMP]: true\n      });\n      if (!ctx || sharedConfig.done) return Comp(props);\n      const c = sharedConfig.context;\n      setHydrateContext(ctx);\n      const r = Comp(props);\n      setHydrateContext(c);\n      return r;\n    }) : \"\");\n  };\n  wrap.preload = () => p || ((p = fn()).then(mod => comp = () => mod.default), p);\n  return wrap;\n}\nlet counter = 0;\nfunction createUniqueId() {\n  const ctx = sharedConfig.context;\n  return ctx ? sharedConfig.getNextContextId() : `cl-${counter++}`;\n}\n\nconst narrowedError = name => `Attempting to access a stale value from <${name}> that could possibly be undefined. This may occur because you are reading the accessor returned from the component at a time where it has already been unmounted. We recommend cleaning up any stale timers or async, or reading from the initial condition.` ;\nfunction For(props) {\n  const fallback = \"fallback\" in props && {\n    fallback: () => props.fallback\n  };\n  return createMemo(mapArray(() => props.each, props.children, fallback || undefined), undefined, {\n    name: \"value\"\n  }) ;\n}\nfunction Index(props) {\n  const fallback = \"fallback\" in props && {\n    fallback: () => props.fallback\n  };\n  return createMemo(indexArray(() => props.each, props.children, fallback || undefined), undefined, {\n    name: \"value\"\n  }) ;\n}\nfunction Show(props) {\n  const keyed = props.keyed;\n  const conditionValue = createMemo(() => props.when, undefined, {\n    name: \"condition value\"\n  } );\n  const condition = keyed ? conditionValue : createMemo(conditionValue, undefined, {\n    equals: (a, b) => !a === !b,\n    name: \"condition\"\n  } );\n  return createMemo(() => {\n    const c = condition();\n    if (c) {\n      const child = props.children;\n      const fn = typeof child === \"function\" && child.length > 0;\n      return fn ? untrack(() => child(keyed ? c : () => {\n        if (!untrack(condition)) throw narrowedError(\"Show\");\n        return conditionValue();\n      })) : child;\n    }\n    return props.fallback;\n  }, undefined, {\n    name: \"value\"\n  } );\n}\nfunction Switch(props) {\n  const chs = children(() => props.children);\n  const switchFunc = createMemo(() => {\n    const ch = chs();\n    const mps = Array.isArray(ch) ? ch : [ch];\n    let func = () => undefined;\n    for (let i = 0; i < mps.length; i++) {\n      const index = i;\n      const mp = mps[i];\n      const prevFunc = func;\n      const conditionValue = createMemo(() => prevFunc() ? undefined : mp.when, undefined, {\n        name: \"condition value\"\n      } );\n      const condition = mp.keyed ? conditionValue : createMemo(conditionValue, undefined, {\n        equals: (a, b) => !a === !b,\n        name: \"condition\"\n      } );\n      func = () => prevFunc() || (condition() ? [index, conditionValue, mp] : undefined);\n    }\n    return func;\n  });\n  return createMemo(() => {\n    const sel = switchFunc()();\n    if (!sel) return props.fallback;\n    const [index, conditionValue, mp] = sel;\n    const child = mp.children;\n    const fn = typeof child === \"function\" && child.length > 0;\n    return fn ? untrack(() => child(mp.keyed ? conditionValue() : () => {\n      if (untrack(switchFunc)()?.[0] !== index) throw narrowedError(\"Match\");\n      return conditionValue();\n    })) : child;\n  }, undefined, {\n    name: \"eval conditions\"\n  } );\n}\nfunction Match(props) {\n  return props;\n}\nlet Errors;\nfunction resetErrorBoundaries() {\n  Errors && [...Errors].forEach(fn => fn());\n}\nfunction ErrorBoundary(props) {\n  let err;\n  if (sharedConfig.context && sharedConfig.load) err = sharedConfig.load(sharedConfig.getContextId());\n  const [errored, setErrored] = createSignal(err, {\n    name: \"errored\"\n  } );\n  Errors || (Errors = new Set());\n  Errors.add(setErrored);\n  onCleanup(() => Errors.delete(setErrored));\n  return createMemo(() => {\n    let e;\n    if (e = errored()) {\n      const f = props.fallback;\n      if ((typeof f !== \"function\" || f.length == 0)) console.error(e);\n      return typeof f === \"function\" && f.length ? untrack(() => f(e, () => setErrored())) : f;\n    }\n    return catchError(() => props.children, setErrored);\n  }, undefined, {\n    name: \"value\"\n  } );\n}\n\nconst suspenseListEquals = (a, b) => a.showContent === b.showContent && a.showFallback === b.showFallback;\nconst SuspenseListContext = /* #__PURE__ */createContext();\nfunction SuspenseList(props) {\n  let [wrapper, setWrapper] = createSignal(() => ({\n      inFallback: false\n    })),\n    show;\n  const listContext = useContext(SuspenseListContext);\n  const [registry, setRegistry] = createSignal([]);\n  if (listContext) {\n    show = listContext.register(createMemo(() => wrapper()().inFallback));\n  }\n  const resolved = createMemo(prev => {\n    const reveal = props.revealOrder,\n      tail = props.tail,\n      {\n        showContent = true,\n        showFallback = true\n      } = show ? show() : {},\n      reg = registry(),\n      reverse = reveal === \"backwards\";\n    if (reveal === \"together\") {\n      const all = reg.every(inFallback => !inFallback());\n      const res = reg.map(() => ({\n        showContent: all && showContent,\n        showFallback\n      }));\n      res.inFallback = !all;\n      return res;\n    }\n    let stop = false;\n    let inFallback = prev.inFallback;\n    const res = [];\n    for (let i = 0, len = reg.length; i < len; i++) {\n      const n = reverse ? len - i - 1 : i,\n        s = reg[n]();\n      if (!stop && !s) {\n        res[n] = {\n          showContent,\n          showFallback\n        };\n      } else {\n        const next = !stop;\n        if (next) inFallback = true;\n        res[n] = {\n          showContent: next,\n          showFallback: !tail || next && tail === \"collapsed\" ? showFallback : false\n        };\n        stop = true;\n      }\n    }\n    if (!stop) inFallback = false;\n    res.inFallback = inFallback;\n    return res;\n  }, {\n    inFallback: false\n  });\n  setWrapper(() => resolved);\n  return createComponent(SuspenseListContext.Provider, {\n    value: {\n      register: inFallback => {\n        let index;\n        setRegistry(registry => {\n          index = registry.length;\n          return [...registry, inFallback];\n        });\n        return createMemo(() => resolved()[index], undefined, {\n          equals: suspenseListEquals\n        });\n      }\n    },\n    get children() {\n      return props.children;\n    }\n  });\n}\nfunction Suspense(props) {\n  let counter = 0,\n    show,\n    ctx,\n    p,\n    flicker,\n    error;\n  const [inFallback, setFallback] = createSignal(false),\n    SuspenseContext = getSuspenseContext(),\n    store = {\n      increment: () => {\n        if (++counter === 1) setFallback(true);\n      },\n      decrement: () => {\n        if (--counter === 0) setFallback(false);\n      },\n      inFallback,\n      effects: [],\n      resolved: false\n    },\n    owner = getOwner();\n  if (sharedConfig.context && sharedConfig.load) {\n    const key = sharedConfig.getContextId();\n    let ref = sharedConfig.load(key);\n    if (ref) {\n      if (typeof ref !== \"object\" || ref.s !== 1) p = ref;else sharedConfig.gather(key);\n    }\n    if (p && p !== \"$$f\") {\n      const [s, set] = createSignal(undefined, {\n        equals: false\n      });\n      flicker = s;\n      p.then(() => {\n        if (sharedConfig.done) return set();\n        sharedConfig.gather(key);\n        setHydrateContext(ctx);\n        set();\n        setHydrateContext();\n      }, err => {\n        error = err;\n        set();\n      });\n    }\n  }\n  const listContext = useContext(SuspenseListContext);\n  if (listContext) show = listContext.register(store.inFallback);\n  let dispose;\n  onCleanup(() => dispose && dispose());\n  return createComponent(SuspenseContext.Provider, {\n    value: store,\n    get children() {\n      return createMemo(() => {\n        if (error) throw error;\n        ctx = sharedConfig.context;\n        if (flicker) {\n          flicker();\n          return flicker = undefined;\n        }\n        if (ctx && p === \"$$f\") setHydrateContext();\n        const rendered = createMemo(() => props.children);\n        return createMemo(prev => {\n          const inFallback = store.inFallback(),\n            {\n              showContent = true,\n              showFallback = true\n            } = show ? show() : {};\n          if ((!inFallback || p && p !== \"$$f\") && showContent) {\n            store.resolved = true;\n            dispose && dispose();\n            dispose = ctx = p = undefined;\n            resumeEffects(store.effects);\n            return rendered();\n          }\n          if (!showFallback) return;\n          if (dispose) return prev;\n          return createRoot(disposer => {\n            dispose = disposer;\n            if (ctx) {\n              setHydrateContext({\n                id: ctx.id + \"F\",\n                count: 0\n              });\n              ctx = undefined;\n            }\n            return props.fallback;\n          }, owner);\n        });\n      });\n    }\n  });\n}\n\nconst DEV = {\n  hooks: DevHooks,\n  writeSignal,\n  registerGraph\n} ;\nif (globalThis) {\n  if (!globalThis.Solid$$) globalThis.Solid$$ = true;else console.warn(\"You appear to have multiple instances of Solid. This can lead to unexpected behavior.\");\n}\n\nexport { $DEVCOMP, $PROXY, $TRACK, DEV, ErrorBoundary, For, Index, Match, Show, Suspense, SuspenseList, Switch, batch, cancelCallback, catchError, children, createComponent, createComputed, createContext, createDeferred, createEffect, createMemo, createReaction, createRenderEffect, createResource, createRoot, createSelector, createSignal, createUniqueId, enableExternalSource, enableHydration, enableScheduling, equalFn, from, getListener, getOwner, indexArray, lazy, mapArray, mergeProps, observable, on, onCleanup, onError, onMount, requestCallback, resetErrorBoundaries, runWithOwner, sharedConfig, splitProps, startTransition, untrack, useContext, useTransition };\n"], "mappings": ";AAAA,IAAI,gBAAgB;AAApB,IACE,sBAAsB;AADxB,IAEE,mBAAmB;AAFrB,IAGE,YAAY,CAAC;AAHf,IAIE,cAAc;AAJhB,IAKE,oBAAoB;AALtB,IAME,gBAAgB;AANlB,IAOE,WAAW;AAPb,IAQE,mBAAmB;AARrB,IASE,cAAc;AAThB,IAUE,mBAAmB;AAVrB,IAWE,oBAAoB;AACtB,IAAM,oBAAoB;AAC1B,SAAS,iBAAiB;AACxB,QAAM,UAAU,IAAI,eAAe,GACjC,OAAO,QAAQ;AACjB,qBAAmB,MAAM,KAAK,YAAY,IAAI;AAC9C,UAAQ,MAAM,YAAY,MAAM;AAC9B,QAAI,sBAAsB,MAAM;AAC9B,YAAM,cAAc,YAAY,IAAI;AACpC,iBAAW,cAAc;AACzB,oBAAc,cAAc;AAC5B,UAAI;AACF,cAAM,cAAc,kBAAkB,WAAW;AACjD,YAAI,CAAC,aAAa;AAChB,8BAAoB;AAAA,QACtB,MAAO,MAAK,YAAY,IAAI;AAAA,MAC9B,SAAS,OAAO;AACd,aAAK,YAAY,IAAI;AACrB,cAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACA,MAAI,aAAa,UAAU,cAAc,UAAU,WAAW,gBAAgB;AAC5E,UAAM,aAAa,UAAU;AAC7B,wBAAoB,MAAM;AACxB,YAAM,cAAc,YAAY,IAAI;AACpC,UAAI,eAAe,UAAU;AAC3B,YAAI,WAAW,eAAe,GAAG;AAC/B,iBAAO;AAAA,QACT;AACA,eAAO,eAAe;AAAA,MACxB,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF,OAAO;AACL,wBAAoB,MAAM,YAAY,IAAI,KAAK;AAAA,EACjD;AACF;AACA,SAAS,QAAQA,YAAW,MAAM;AAChC,WAAS,YAAY;AACnB,QAAI,IAAI;AACR,QAAI,IAAIA,WAAU,SAAS;AAC3B,WAAO,KAAK,GAAG;AACb,YAAM,IAAI,IAAI,KAAK;AACnB,YAAM,MAAM,KAAK,iBAAiBA,WAAU,CAAC,EAAE;AAC/C,UAAI,MAAM,EAAG,KAAI,IAAI;AAAA,eAAW,MAAM,EAAG,KAAI,IAAI;AAAA,UAAO,QAAO;AAAA,IACjE;AACA,WAAO;AAAA,EACT;AACA,EAAAA,WAAU,OAAO,UAAU,GAAG,GAAG,IAAI;AACvC;AACA,SAAS,gBAAgB,IAAI,SAAS;AACpC,MAAI,CAAC,iBAAkB,gBAAe;AACtC,MAAI,YAAY,YAAY,IAAI,GAC9B,UAAU;AACZ,MAAI,WAAW,QAAQ,QAAS,WAAU,QAAQ;AAClD,QAAM,UAAU;AAAA,IACd,IAAI;AAAA,IACJ;AAAA,IACA;AAAA,IACA,gBAAgB,YAAY;AAAA,EAC9B;AACA,UAAQ,WAAW,OAAO;AAC1B,MAAI,CAAC,uBAAuB,CAAC,kBAAkB;AAC7C,0BAAsB;AACtB,wBAAoB;AACpB,qBAAiB;AAAA,EACnB;AACA,SAAO;AACT;AACA,SAAS,eAAe,MAAM;AAC5B,OAAK,KAAK;AACZ;AACA,SAAS,UAAU,aAAa;AAC9B,wBAAsB;AACtB,qBAAmB;AACnB,MAAI;AACF,WAAO,SAAS,WAAW;AAAA,EAC7B,UAAE;AACA,kBAAc;AACd,uBAAmB;AAAA,EACrB;AACF;AACA,SAAS,SAAS,aAAa;AAC7B,MAAI,cAAc;AAClB,gBAAc,UAAU,CAAC,KAAK;AAC9B,SAAO,gBAAgB,MAAM;AAC3B,QAAI,YAAY,iBAAiB,eAAe,kBAAkB,GAAG;AACnE;AAAA,IACF;AACA,UAAM,WAAW,YAAY;AAC7B,QAAI,aAAa,MAAM;AACrB,kBAAY,KAAK;AACjB,YAAM,yBAAyB,YAAY,kBAAkB;AAC7D,eAAS,sBAAsB;AAC/B,oBAAc,YAAY,IAAI;AAC9B,UAAI,gBAAgB,UAAU,CAAC,GAAG;AAChC,kBAAU,MAAM;AAAA,MAClB;AAAA,IACF,MAAO,WAAU,MAAM;AACvB,kBAAc,UAAU,CAAC,KAAK;AAAA,EAChC;AACA,SAAO,gBAAgB;AACzB;AAEA,IAAM,eAAe;AAAA,EACnB,SAAS;AAAA,EACT,UAAU;AAAA,EACV,SAAS;AAAA,EACT,MAAM;AAAA,EACN,eAAe;AACb,WAAO,aAAa,KAAK,QAAQ,KAAK;AAAA,EACxC;AAAA,EACA,mBAAmB;AACjB,WAAO,aAAa,KAAK,QAAQ,OAAO;AAAA,EAC1C;AACF;AACA,SAAS,aAAa,OAAO;AAC3B,QAAM,MAAM,OAAO,KAAK,GACtB,MAAM,IAAI,SAAS;AACrB,SAAO,aAAa,QAAQ,MAAM,MAAM,OAAO,aAAa,KAAK,GAAG,IAAI,MAAM;AAChF;AACA,SAAS,kBAAkB,SAAS;AAClC,eAAa,UAAU;AACzB;AACA,SAAS,qBAAqB;AAC5B,SAAO;AAAA,IACL,GAAG,aAAa;AAAA,IAChB,IAAI,aAAa,iBAAiB;AAAA,IAClC,OAAO;AAAA,EACT;AACF;AAEA,IAAM,SAAS;AACf,IAAM,UAAU,CAAC,GAAG,MAAM,MAAM;AAChC,IAAM,SAAS,OAAO,aAAa;AACnC,IAAM,iBAAiB,OAAO,UAAU;AACxC,IAAM,SAAS,OAAO,aAAa;AACnC,IAAM,WAAW,OAAO,qBAAqB;AAC7C,IAAM,gBAAgB;AAAA,EACpB,QAAQ;AACV;AACA,IAAI,QAAQ;AACZ,IAAI,aAAa;AACjB,IAAM,QAAQ;AACd,IAAM,UAAU;AAChB,IAAM,UAAU;AAAA,EACd,OAAO;AAAA,EACP,UAAU;AAAA,EACV,SAAS;AAAA,EACT,OAAO;AACT;AACA,IAAM,UAAU,CAAC;AACjB,IAAI,QAAQ;AACZ,IAAI,aAAa;AACjB,IAAI,YAAY;AAChB,IAAI,uBAAuB;AAC3B,IAAI,WAAW;AACf,IAAI,UAAU;AACd,IAAI,UAAU;AACd,IAAI,YAAY;AAChB,IAAM,WAAW;AAAA,EACf,aAAa;AAAA,EACb,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,oBAAoB;AACtB;AACA,SAAS,WAAW,IAAI,eAAe;AACrC,QAAM,WAAW,UACf,QAAQ,OACR,UAAU,GAAG,WAAW,GACxB,UAAU,kBAAkB,SAAY,QAAQ,eAChD,OAAO,UAAU;AAAA,IACf,OAAO;AAAA,IACP,UAAU;AAAA,IACV,SAAS;AAAA,IACT,OAAO;AAAA,EACT,IAAK;AAAA,IACH,OAAO;AAAA,IACP,UAAU;AAAA,IACV,SAAS,UAAU,QAAQ,UAAU;AAAA,IACrC,OAAO;AAAA,EACT,GACA,WAAW,UAAU,MAAM,GAAG,MAAM;AAClC,UAAM,IAAI,MAAM,oEAAoE;AAAA,EACtF,CAAC,IAAK,MAAM,GAAG,MAAM,QAAQ,MAAM,UAAU,IAAI,CAAC,CAAC;AACrD,WAAS,oBAAoB,SAAS,iBAAiB,IAAI;AAC3D,UAAQ;AACR,aAAW;AACX,MAAI;AACF,WAAO,WAAW,UAAU,IAAI;AAAA,EAClC,UAAE;AACA,eAAW;AACX,YAAQ;AAAA,EACV;AACF;AACA,SAAS,aAAa,OAAO,SAAS;AACpC,YAAU,UAAU,OAAO,OAAO,CAAC,GAAG,eAAe,OAAO,IAAI;AAChE,QAAM,IAAI;AAAA,IACR;AAAA,IACA,WAAW;AAAA,IACX,eAAe;AAAA,IACf,YAAY,QAAQ,UAAU;AAAA,EAChC;AACA;AACE,QAAI,QAAQ,KAAM,GAAE,OAAO,QAAQ;AACnC,QAAI,QAAQ,UAAU;AACpB,QAAE,WAAW;AAAA,IACf,OAAO;AACL,oBAAc,CAAC;AACf,UAAI,SAAS,kBAAmB,UAAS,kBAAkB,CAAC;AAAA,IAC9D;AAAA,EACF;AACA,QAAM,SAAS,CAAAC,WAAS;AACtB,QAAI,OAAOA,WAAU,YAAY;AAC/B,UAAI,cAAc,WAAW,WAAW,WAAW,QAAQ,IAAI,CAAC,EAAG,CAAAA,SAAQA,OAAM,EAAE,MAAM;AAAA,UAAO,CAAAA,SAAQA,OAAM,EAAE,KAAK;AAAA,IACvH;AACA,WAAO,YAAY,GAAGA,MAAK;AAAA,EAC7B;AACA,SAAO,CAAC,WAAW,KAAK,CAAC,GAAG,MAAM;AACpC;AACA,SAAS,eAAe,IAAI,OAAO,SAAS;AAC1C,QAAM,IAAI,kBAAkB,IAAI,OAAO,MAAM,OAAO,OAAQ;AAC5D,MAAI,aAAa,cAAc,WAAW,QAAS,SAAQ,KAAK,CAAC;AAAA,MAAO,mBAAkB,CAAC;AAC7F;AACA,SAAS,mBAAmB,IAAI,OAAO,SAAS;AAC9C,QAAM,IAAI,kBAAkB,IAAI,OAAO,OAAO,OAAO,OAAQ;AAC7D,MAAI,aAAa,cAAc,WAAW,QAAS,SAAQ,KAAK,CAAC;AAAA,MAAO,mBAAkB,CAAC;AAC7F;AACA,SAAS,aAAa,IAAI,OAAO,SAAS;AACxC,eAAa;AACb,QAAM,IAAI,kBAAkB,IAAI,OAAO,OAAO,OAAO,OAAQ,GAC3D,IAAI,mBAAmB,WAAW,eAAe;AACnD,MAAI,EAAG,GAAE,WAAW;AACpB,MAAI,CAAC,WAAW,CAAC,QAAQ,OAAQ,GAAE,OAAO;AAC1C,YAAU,QAAQ,KAAK,CAAC,IAAI,kBAAkB,CAAC;AACjD;AACA,SAAS,eAAe,cAAc,SAAS;AAC7C,MAAI;AACJ,QAAM,IAAI,kBAAkB,MAAM;AAC9B,SAAK,GAAG,IAAI,QAAQ,YAAY;AAChC,SAAK;AAAA,EACP,GAAG,QAAW,OAAO,GAAG,OAAQ,GAChC,IAAI,mBAAmB,WAAW,eAAe;AACnD,MAAI,EAAG,GAAE,WAAW;AACpB,IAAE,OAAO;AACT,SAAO,cAAY;AACjB,SAAK;AACL,sBAAkB,CAAC;AAAA,EACrB;AACF;AACA,SAAS,WAAW,IAAI,OAAO,SAAS;AACtC,YAAU,UAAU,OAAO,OAAO,CAAC,GAAG,eAAe,OAAO,IAAI;AAChE,QAAM,IAAI,kBAAkB,IAAI,OAAO,MAAM,GAAG,OAAQ;AACxD,IAAE,YAAY;AACd,IAAE,gBAAgB;AAClB,IAAE,aAAa,QAAQ,UAAU;AACjC,MAAI,aAAa,cAAc,WAAW,SAAS;AACjD,MAAE,SAAS;AACX,YAAQ,KAAK,CAAC;AAAA,EAChB,MAAO,mBAAkB,CAAC;AAC1B,SAAO,WAAW,KAAK,CAAC;AAC1B;AACA,SAAS,UAAU,GAAG;AACpB,SAAO,KAAK,OAAO,MAAM,YAAY,UAAU;AACjD;AACA,SAAS,eAAe,SAAS,UAAU,UAAU;AACnD,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,OAAO,aAAa,YAAY;AAClC,aAAS;AACT,cAAU;AACV,cAAU,YAAY,CAAC;AAAA,EACzB,OAAO;AACL,aAAS;AACT,cAAU;AACV,cAAU,YAAY,CAAC;AAAA,EACzB;AACA,MAAI,KAAK,MACP,QAAQ,SACR,KAAK,MACL,wBAAwB,OACxB,YAAY,OACZ,WAAW,kBAAkB,SAC7B,UAAU,OAAO,WAAW,cAAc,WAAW,MAAM;AAC7D,QAAM,WAAW,oBAAI,IAAI,GACvB,CAAC,OAAO,QAAQ,KAAK,QAAQ,WAAW,cAAc,QAAQ,YAAY,GAC1E,CAAC,OAAO,QAAQ,IAAI,aAAa,MAAS,GAC1C,CAAC,OAAO,OAAO,IAAI,aAAa,QAAW;AAAA,IACzC,QAAQ;AAAA,EACV,CAAC,GACD,CAAC,OAAO,QAAQ,IAAI,aAAa,WAAW,UAAU,YAAY;AACpE,MAAI,aAAa,SAAS;AACxB,SAAK,aAAa,iBAAiB;AACnC,QAAI,QAAQ,gBAAgB,UAAW,SAAQ,QAAQ;AAAA,aAAsB,aAAa,QAAQ,aAAa,IAAI,EAAE,EAAG,SAAQ,aAAa,KAAK,EAAE;AAAA,EACtJ;AACA,WAAS,QAAQ,GAAG,GAAGC,QAAO,KAAK;AACjC,QAAI,OAAO,GAAG;AACZ,WAAK;AACL,cAAQ,WAAc,WAAW;AACjC,WAAK,MAAM,SAAS,MAAM,UAAU,QAAQ,WAAY,gBAAe,MAAM,QAAQ,WAAW,KAAK;AAAA,QACnG,OAAO;AAAA,MACT,CAAC,CAAC;AACF,cAAQ;AACR,UAAI,cAAc,KAAK,uBAAuB;AAC5C,mBAAW,SAAS,OAAO,CAAC;AAC5B,gCAAwB;AACxB,mBAAW,MAAM;AACf,qBAAW,UAAU;AACrB,uBAAa,GAAGA,MAAK;AAAA,QACvB,GAAG,KAAK;AAAA,MACV,MAAO,cAAa,GAAGA,MAAK;AAAA,IAC9B;AACA,WAAO;AAAA,EACT;AACA,WAAS,aAAa,GAAG,KAAK;AAC5B,eAAW,MAAM;AACf,UAAI,QAAQ,OAAW,UAAS,MAAM,CAAC;AACvC,eAAS,QAAQ,SAAY,YAAY,WAAW,UAAU,YAAY;AAC1E,eAAS,GAAG;AACZ,iBAAW,KAAK,SAAS,KAAK,EAAG,GAAE,UAAU;AAC7C,eAAS,MAAM;AAAA,IACjB,GAAG,KAAK;AAAA,EACV;AACA,WAAS,OAAO;AACd,UAAM,IAAI,mBAAmB,WAAW,eAAe,GACrD,IAAI,MAAM,GACV,MAAM,MAAM;AACd,QAAI,QAAQ,UAAa,CAAC,GAAI,OAAM;AACpC,QAAI,YAAY,CAAC,SAAS,QAAQ,GAAG;AACnC,qBAAe,MAAM;AACnB,cAAM;AACN,YAAI,IAAI;AACN,cAAI,EAAE,YAAY,cAAc,sBAAuB,YAAW,SAAS,IAAI,EAAE;AAAA,mBAAW,CAAC,SAAS,IAAI,CAAC,GAAG;AAC5G,cAAE,UAAU;AACZ,qBAAS,IAAI,CAAC;AAAA,UAChB;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AACA,WAAS,KAAK,aAAa,MAAM;AAC/B,QAAI,eAAe,SAAS,UAAW;AACvC,gBAAY;AACZ,UAAM,SAAS,UAAU,QAAQ,IAAI;AACrC,4BAAwB,cAAc,WAAW;AACjD,QAAI,UAAU,QAAQ,WAAW,OAAO;AACtC,cAAQ,IAAI,QAAQ,KAAK,CAAC;AAC1B;AAAA,IACF;AACA,QAAI,cAAc,GAAI,YAAW,SAAS,OAAO,EAAE;AACnD,QAAIA;AACJ,UAAM,IAAI,UAAU,UAAU,QAAQ,QAAQ,MAAM;AAClD,UAAI;AACF,eAAO,QAAQ,QAAQ;AAAA,UACrB,OAAO,MAAM;AAAA,UACb;AAAA,QACF,CAAC;AAAA,MACH,SAAS,cAAc;AACrB,QAAAA,SAAQ;AAAA,MACV;AAAA,IACF,CAAC;AACD,QAAIA,WAAU,QAAW;AACvB,cAAQ,IAAI,QAAW,UAAUA,MAAK,GAAG,MAAM;AAC/C;AAAA,IACF,WAAW,CAAC,UAAU,CAAC,GAAG;AACxB,cAAQ,IAAI,GAAG,QAAW,MAAM;AAChC,aAAO;AAAA,IACT;AACA,SAAK;AACL,QAAI,OAAO,GAAG;AACZ,UAAI,EAAE,MAAM,EAAG,SAAQ,IAAI,EAAE,GAAG,QAAW,MAAM;AAAA,UAAO,SAAQ,IAAI,QAAW,UAAU,EAAE,CAAC,GAAG,MAAM;AACrG,aAAO;AAAA,IACT;AACA,gBAAY;AACZ,mBAAe,MAAM,YAAY,KAAK;AACtC,eAAW,MAAM;AACf,eAAS,WAAW,eAAe,SAAS;AAC5C,cAAQ;AAAA,IACV,GAAG,KAAK;AACR,WAAO,EAAE,KAAK,OAAK,QAAQ,GAAG,GAAG,QAAW,MAAM,GAAG,OAAK,QAAQ,GAAG,QAAW,UAAU,CAAC,GAAG,MAAM,CAAC;AAAA,EACvG;AACA,SAAO,iBAAiB,MAAM;AAAA,IAC5B,OAAO;AAAA,MACL,KAAK,MAAM,MAAM;AAAA,IACnB;AAAA,IACA,OAAO;AAAA,MACL,KAAK,MAAM,MAAM;AAAA,IACnB;AAAA,IACA,SAAS;AAAA,MACP,MAAM;AACJ,cAAM,IAAI,MAAM;AAChB,eAAO,MAAM,aAAa,MAAM;AAAA,MAClC;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,MAAM;AACJ,YAAI,CAAC,SAAU,QAAO,KAAK;AAC3B,cAAM,MAAM,MAAM;AAClB,YAAI,OAAO,CAAC,GAAI,OAAM;AACtB,eAAO,MAAM;AAAA,MACf;AAAA,IACF;AAAA,EACF,CAAC;AACD,MAAI,QAAQ;AACZ,MAAI,QAAS,gBAAe,OAAO,QAAQ,OAAO,KAAK,KAAK,EAAE;AAAA,MAAO,MAAK,KAAK;AAC/E,SAAO,CAAC,MAAM;AAAA,IACZ,SAAS,UAAQ,aAAa,OAAO,MAAM,KAAK,IAAI,CAAC;AAAA,IACrD,QAAQ;AAAA,EACV,CAAC;AACH;AACA,SAAS,eAAe,QAAQ,SAAS;AACvC,MAAI,GACF,UAAU,UAAU,QAAQ,YAAY;AAC1C,QAAM,OAAO,kBAAkB,MAAM;AACnC,QAAI,CAAC,KAAK,CAAC,EAAE,GAAI,KAAI,gBAAgB,MAAM,YAAY,MAAM,KAAK,KAAK,GAAG,YAAY,SAAY;AAAA,MAChG;AAAA,IACF,IAAI,MAAS;AACb,WAAO,OAAO;AAAA,EAChB,GAAG,QAAW,IAAI;AAClB,QAAM,CAAC,UAAU,WAAW,IAAI,aAAa,cAAc,WAAW,WAAW,WAAW,QAAQ,IAAI,IAAI,IAAI,KAAK,SAAS,KAAK,OAAO,OAAO;AACjJ,oBAAkB,IAAI;AACtB,cAAY,MAAM,cAAc,WAAW,WAAW,WAAW,QAAQ,IAAI,IAAI,IAAI,KAAK,SAAS,KAAK,KAAK;AAC7G,SAAO;AACT;AACA,SAAS,eAAe,QAAQ,KAAK,SAAS,SAAS;AACrD,QAAM,OAAO,oBAAI,IAAI;AACrB,QAAM,OAAO,kBAAkB,OAAK;AAClC,UAAM,IAAI,OAAO;AACjB,eAAW,CAAC,KAAK,GAAG,KAAK,KAAK,QAAQ,EAAG,KAAI,GAAG,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,GAAG;AACtE,iBAAW,KAAK,IAAI,OAAO,GAAG;AAC5B,UAAE,QAAQ;AACV,YAAI,EAAE,KAAM,SAAQ,KAAK,CAAC;AAAA,YAAO,SAAQ,KAAK,CAAC;AAAA,MACjD;AAAA,IACF;AACA,WAAO;AAAA,EACT,GAAG,QAAW,MAAM,OAAO,OAAQ;AACnC,oBAAkB,IAAI;AACtB,SAAO,SAAO;AACZ,UAAM,WAAW;AACjB,QAAI,UAAU;AACZ,UAAI;AACJ,UAAI,IAAI,KAAK,IAAI,GAAG,EAAG,GAAE,IAAI,QAAQ;AAAA,UAAO,MAAK,IAAI,KAAK,IAAI,oBAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;AACjF,gBAAU,MAAM;AACd,UAAE,OAAO,QAAQ;AACjB,SAAC,EAAE,QAAQ,KAAK,OAAO,GAAG;AAAA,MAC5B,CAAC;AAAA,IACH;AACA,WAAO,GAAG,KAAK,cAAc,WAAW,WAAW,WAAW,QAAQ,IAAI,IAAI,IAAI,KAAK,SAAS,KAAK,KAAK;AAAA,EAC5G;AACF;AACA,SAAS,MAAM,IAAI;AACjB,SAAO,WAAW,IAAI,KAAK;AAC7B;AACA,SAAS,QAAQ,IAAI;AACnB,MAAI,CAAC,wBAAwB,aAAa,KAAM,QAAO,GAAG;AAC1D,QAAM,WAAW;AACjB,aAAW;AACX,MAAI;AACF,QAAI,qBAAsB,QAAO,qBAAqB,QAAQ,EAAE;AAChE,WAAO,GAAG;AAAA,EACZ,UAAE;AACA,eAAW;AAAA,EACb;AACF;AACA,SAAS,GAAG,MAAM,IAAI,SAAS;AAC7B,QAAM,UAAU,MAAM,QAAQ,IAAI;AAClC,MAAI;AACJ,MAAI,QAAQ,WAAW,QAAQ;AAC/B,SAAO,eAAa;AAClB,QAAI;AACJ,QAAI,SAAS;AACX,cAAQ,MAAM,KAAK,MAAM;AACzB,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAK,OAAM,CAAC,IAAI,KAAK,CAAC,EAAE;AAAA,IAC3D,MAAO,SAAQ,KAAK;AACpB,QAAI,OAAO;AACT,cAAQ;AACR,aAAO;AAAA,IACT;AACA,UAAM,SAAS,QAAQ,MAAM,GAAG,OAAO,WAAW,SAAS,CAAC;AAC5D,gBAAY;AACZ,WAAO;AAAA,EACT;AACF;AACA,SAAS,QAAQ,IAAI;AACnB,eAAa,MAAM,QAAQ,EAAE,CAAC;AAChC;AACA,SAAS,UAAU,IAAI;AACrB,MAAI,UAAU,KAAM,SAAQ,KAAK,uEAAuE;AAAA,WAAW,MAAM,aAAa,KAAM,OAAM,WAAW,CAAC,EAAE;AAAA,MAAO,OAAM,SAAS,KAAK,EAAE;AAC7L,SAAO;AACT;AACA,SAAS,WAAW,IAAI,SAAS;AAC/B,YAAU,QAAQ,OAAO,OAAO;AAChC,UAAQ,kBAAkB,QAAW,QAAW,IAAI;AACpD,QAAM,UAAU;AAAA,IACd,GAAG,MAAM;AAAA,IACT,CAAC,KAAK,GAAG,CAAC,OAAO;AAAA,EACnB;AACA,MAAI,cAAc,WAAW,QAAS,YAAW,QAAQ,IAAI,KAAK;AAClE,MAAI;AACF,WAAO,GAAG;AAAA,EACZ,SAAS,KAAK;AACZ,gBAAY,GAAG;AAAA,EACjB,UAAE;AACA,YAAQ,MAAM;AAAA,EAChB;AACF;AACA,SAAS,cAAc;AACrB,SAAO;AACT;AACA,SAAS,WAAW;AAClB,SAAO;AACT;AACA,SAAS,aAAa,GAAG,IAAI;AAC3B,QAAM,OAAO;AACb,QAAM,eAAe;AACrB,UAAQ;AACR,aAAW;AACX,MAAI;AACF,WAAO,WAAW,IAAI,IAAI;AAAA,EAC5B,SAAS,KAAK;AACZ,gBAAY,GAAG;AAAA,EACjB,UAAE;AACA,YAAQ;AACR,eAAW;AAAA,EACb;AACF;AACA,SAAS,iBAAiB,YAAY,iBAAiB;AACrD,cAAY;AACd;AACA,SAAS,gBAAgB,IAAI;AAC3B,MAAI,cAAc,WAAW,SAAS;AACpC,OAAG;AACH,WAAO,WAAW;AAAA,EACpB;AACA,QAAM,IAAI;AACV,QAAM,IAAI;AACV,SAAO,QAAQ,QAAQ,EAAE,KAAK,MAAM;AAClC,eAAW;AACX,YAAQ;AACR,QAAI;AACJ,QAAI,aAAa,iBAAiB;AAChC,UAAI,eAAe,aAAa;AAAA,QAC9B,SAAS,oBAAI,IAAI;AAAA,QACjB,SAAS,CAAC;AAAA,QACV,UAAU,oBAAI,IAAI;AAAA,QAClB,UAAU,oBAAI,IAAI;AAAA,QAClB,OAAO,oBAAI,IAAI;AAAA,QACf,SAAS;AAAA,MACX;AACA,QAAE,SAAS,EAAE,OAAO,IAAI,QAAQ,SAAO,EAAE,UAAU,GAAG;AACtD,QAAE,UAAU;AAAA,IACd;AACA,eAAW,IAAI,KAAK;AACpB,eAAW,QAAQ;AACnB,WAAO,IAAI,EAAE,OAAO;AAAA,EACtB,CAAC;AACH;AACA,IAAM,CAAC,cAAc,eAAe,IAAiB,aAAa,KAAK;AACvE,SAAS,gBAAgB;AACvB,SAAO,CAAC,cAAc,eAAe;AACvC;AACA,SAAS,cAAc,GAAG;AACxB,UAAQ,KAAK,MAAM,SAAS,CAAC;AAC7B,IAAE,SAAS;AACb;AACA,SAAS,aAAa,MAAM,OAAO;AACjC,QAAM,IAAI,kBAAkB,MAAM,QAAQ,MAAM;AAC9C,WAAO,OAAO,MAAM;AAAA,MAClB,CAAC,QAAQ,GAAG;AAAA,IACd,CAAC;AACD,WAAO,KAAK,KAAK;AAAA,EACnB,CAAC,GAAG,QAAW,MAAM,CAAC;AACtB,IAAE,QAAQ;AACV,IAAE,YAAY;AACd,IAAE,gBAAgB;AAClB,IAAE,OAAO,KAAK;AACd,IAAE,YAAY;AACd,oBAAkB,CAAC;AACnB,SAAO,EAAE,WAAW,SAAY,EAAE,SAAS,EAAE;AAC/C;AACA,SAAS,cAAc,OAAO;AAC5B,MAAI,OAAO;AACT,QAAI,MAAM,UAAW,OAAM,UAAU,KAAK,KAAK;AAAA,QAAO,OAAM,YAAY,CAAC,KAAK;AAC9E,UAAM,QAAQ;AAAA,EAChB;AACA,MAAI,SAAS,mBAAoB,UAAS,mBAAmB,KAAK;AACpE;AACA,SAAS,cAAc,cAAc,SAAS;AAC5C,QAAM,KAAK,OAAO,SAAS;AAC3B,SAAO;AAAA,IACL;AAAA,IACA,UAAU,eAAe,IAAI,OAAO;AAAA,IACpC;AAAA,EACF;AACF;AACA,SAAS,WAAW,SAAS;AAC3B,MAAI;AACJ,SAAO,SAAS,MAAM,YAAY,QAAQ,MAAM,QAAQ,QAAQ,EAAE,OAAO,SAAY,QAAQ,QAAQ;AACvG;AACA,SAAS,SAAS,IAAI;AACpB,QAAMC,YAAW,WAAW,EAAE;AAC9B,QAAM,OAAO,WAAW,MAAM,gBAAgBA,UAAS,CAAC,GAAG,QAAW;AAAA,IACpE,MAAM;AAAA,EACR,CAAC;AACD,OAAK,UAAU,MAAM;AACnB,UAAM,IAAI,KAAK;AACf,WAAO,MAAM,QAAQ,CAAC,IAAI,IAAI,KAAK,OAAO,CAAC,CAAC,IAAI,CAAC;AAAA,EACnD;AACA,SAAO;AACT;AACA,IAAI;AACJ,SAAS,qBAAqB;AAC5B,SAAO,oBAAoB,kBAAkB,cAAc;AAC7D;AACA,SAAS,qBAAqB,SAASC,WAAU,QAAM,GAAG,GAAG;AAC3D,MAAI,sBAAsB;AACxB,UAAM;AAAA,MACJ,SAAS;AAAA,MACT,SAAS;AAAA,IACX,IAAI;AACJ,2BAAuB;AAAA,MACrB,SAAS,CAAC,IAAI,YAAY;AACxB,cAAM,YAAY,WAAW,IAAI,OAAO;AACxC,cAAM,SAAS,QAAQ,OAAK,UAAU,MAAM,CAAC,GAAG,OAAO;AACvD,eAAO;AAAA,UACL,OAAO,OAAK,OAAO,MAAM,CAAC;AAAA,UAC1B,UAAU;AACR,mBAAO,QAAQ;AACf,sBAAU,QAAQ;AAAA,UACpB;AAAA,QACF;AAAA,MACF;AAAA,MACA,SAAS,QAAM,WAAW,MAAMA,SAAQ,EAAE,CAAC;AAAA,IAC7C;AAAA,EACF,OAAO;AACL,2BAAuB;AAAA,MACrB;AAAA,MACA,SAAAA;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,aAAa;AACpB,QAAM,oBAAoB,cAAc,WAAW;AACnD,MAAI,KAAK,YAAY,oBAAoB,KAAK,SAAS,KAAK,QAAQ;AAClE,SAAK,oBAAoB,KAAK,SAAS,KAAK,WAAW,MAAO,mBAAkB,IAAI;AAAA,SAAO;AACzF,YAAM,UAAU;AAChB,gBAAU;AACV,iBAAW,MAAM,aAAa,IAAI,GAAG,KAAK;AAC1C,gBAAU;AAAA,IACZ;AAAA,EACF;AACA,MAAI,UAAU;AACZ,UAAM,QAAQ,KAAK,YAAY,KAAK,UAAU,SAAS;AACvD,QAAI,CAAC,SAAS,SAAS;AACrB,eAAS,UAAU,CAAC,IAAI;AACxB,eAAS,cAAc,CAAC,KAAK;AAAA,IAC/B,OAAO;AACL,eAAS,QAAQ,KAAK,IAAI;AAC1B,eAAS,YAAY,KAAK,KAAK;AAAA,IACjC;AACA,QAAI,CAAC,KAAK,WAAW;AACnB,WAAK,YAAY,CAAC,QAAQ;AAC1B,WAAK,gBAAgB,CAAC,SAAS,QAAQ,SAAS,CAAC;AAAA,IACnD,OAAO;AACL,WAAK,UAAU,KAAK,QAAQ;AAC5B,WAAK,cAAc,KAAK,SAAS,QAAQ,SAAS,CAAC;AAAA,IACrD;AAAA,EACF;AACA,MAAI,qBAAqB,WAAW,QAAQ,IAAI,IAAI,EAAG,QAAO,KAAK;AACnE,SAAO,KAAK;AACd;AACA,SAAS,YAAY,MAAM,OAAO,QAAQ;AACxC,MAAI,UAAU,cAAc,WAAW,WAAW,WAAW,QAAQ,IAAI,IAAI,IAAI,KAAK,SAAS,KAAK;AACpG,MAAI,CAAC,KAAK,cAAc,CAAC,KAAK,WAAW,SAAS,KAAK,GAAG;AACxD,QAAI,YAAY;AACd,YAAM,oBAAoB,WAAW;AACrC,UAAI,qBAAqB,CAAC,UAAU,WAAW,QAAQ,IAAI,IAAI,GAAG;AAChE,mBAAW,QAAQ,IAAI,IAAI;AAC3B,aAAK,SAAS;AAAA,MAChB;AACA,UAAI,CAAC,kBAAmB,MAAK,QAAQ;AAAA,IACvC,MAAO,MAAK,QAAQ;AACpB,QAAI,KAAK,aAAa,KAAK,UAAU,QAAQ;AAC3C,iBAAW,MAAM;AACf,iBAAS,IAAI,GAAG,IAAI,KAAK,UAAU,QAAQ,KAAK,GAAG;AACjD,gBAAM,IAAI,KAAK,UAAU,CAAC;AAC1B,gBAAM,oBAAoB,cAAc,WAAW;AACnD,cAAI,qBAAqB,WAAW,SAAS,IAAI,CAAC,EAAG;AACrD,cAAI,oBAAoB,CAAC,EAAE,SAAS,CAAC,EAAE,OAAO;AAC5C,gBAAI,EAAE,KAAM,SAAQ,KAAK,CAAC;AAAA,gBAAO,SAAQ,KAAK,CAAC;AAC/C,gBAAI,EAAE,UAAW,gBAAe,CAAC;AAAA,UACnC;AACA,cAAI,CAAC,kBAAmB,GAAE,QAAQ;AAAA,cAAW,GAAE,SAAS;AAAA,QAC1D;AACA,YAAI,QAAQ,SAAS,KAAM;AACzB,oBAAU,CAAC;AACX,cAAI,OAAQ,OAAM,IAAI,MAAM,mCAAmC;AAC/D,gBAAM,IAAI,MAAM;AAAA,QAClB;AAAA,MACF,GAAG,KAAK;AAAA,IACV;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,kBAAkB,MAAM;AAC/B,MAAI,CAAC,KAAK,GAAI;AACd,YAAU,IAAI;AACd,QAAM,OAAO;AACb,iBAAe,MAAM,cAAc,WAAW,WAAW,WAAW,QAAQ,IAAI,IAAI,IAAI,KAAK,SAAS,KAAK,OAAO,IAAI;AACtH,MAAI,cAAc,CAAC,WAAW,WAAW,WAAW,QAAQ,IAAI,IAAI,GAAG;AACrE,mBAAe,MAAM;AACnB,iBAAW,MAAM;AACf,uBAAe,WAAW,UAAU;AACpC,mBAAW,QAAQ;AACnB,uBAAe,MAAM,KAAK,QAAQ,IAAI;AACtC,mBAAW,QAAQ;AAAA,MACrB,GAAG,KAAK;AAAA,IACV,CAAC;AAAA,EACH;AACF;AACA,SAAS,eAAe,MAAM,OAAO,MAAM;AACzC,MAAI;AACJ,QAAM,QAAQ,OACZ,WAAW;AACb,aAAW,QAAQ;AACnB,MAAI;AACF,gBAAY,KAAK,GAAG,KAAK;AAAA,EAC3B,SAAS,KAAK;AACZ,QAAI,KAAK,MAAM;AACb,UAAI,cAAc,WAAW,SAAS;AACpC,aAAK,SAAS;AACd,aAAK,UAAU,KAAK,OAAO,QAAQ,SAAS;AAC5C,aAAK,SAAS;AAAA,MAChB,OAAO;AACL,aAAK,QAAQ;AACb,aAAK,SAAS,KAAK,MAAM,QAAQ,SAAS;AAC1C,aAAK,QAAQ;AAAA,MACf;AAAA,IACF;AACA,SAAK,YAAY,OAAO;AACxB,WAAO,YAAY,GAAG;AAAA,EACxB,UAAE;AACA,eAAW;AACX,YAAQ;AAAA,EACV;AACA,MAAI,CAAC,KAAK,aAAa,KAAK,aAAa,MAAM;AAC7C,QAAI,KAAK,aAAa,QAAQ,eAAe,MAAM;AACjD,kBAAY,MAAM,WAAW,IAAI;AAAA,IACnC,WAAW,cAAc,WAAW,WAAW,KAAK,MAAM;AACxD,iBAAW,QAAQ,IAAI,IAAI;AAC3B,WAAK,SAAS;AAAA,IAChB,MAAO,MAAK,QAAQ;AACpB,SAAK,YAAY;AAAA,EACnB;AACF;AACA,SAAS,kBAAkB,IAAI,MAAM,MAAM,QAAQ,OAAO,SAAS;AACjE,QAAM,IAAI;AAAA,IACR;AAAA,IACA;AAAA,IACA,WAAW;AAAA,IACX,OAAO;AAAA,IACP,SAAS;AAAA,IACT,aAAa;AAAA,IACb,UAAU;AAAA,IACV,OAAO;AAAA,IACP,OAAO;AAAA,IACP,SAAS,QAAQ,MAAM,UAAU;AAAA,IACjC;AAAA,EACF;AACA,MAAI,cAAc,WAAW,SAAS;AACpC,MAAE,QAAQ;AACV,MAAE,SAAS;AAAA,EACb;AACA,MAAI,UAAU,KAAM,SAAQ,KAAK,gFAAgF;AAAA,WAAW,UAAU,SAAS;AAC7I,QAAI,cAAc,WAAW,WAAW,MAAM,MAAM;AAClD,UAAI,CAAC,MAAM,OAAQ,OAAM,SAAS,CAAC,CAAC;AAAA,UAAO,OAAM,OAAO,KAAK,CAAC;AAAA,IAChE,OAAO;AACL,UAAI,CAAC,MAAM,MAAO,OAAM,QAAQ,CAAC,CAAC;AAAA,UAAO,OAAM,MAAM,KAAK,CAAC;AAAA,IAC7D;AAAA,EACF;AACA,MAAI,WAAW,QAAQ,KAAM,GAAE,OAAO,QAAQ;AAC9C,MAAI,wBAAwB,EAAE,IAAI;AAChC,UAAM,CAAC,OAAO,OAAO,IAAI,aAAa,QAAW;AAAA,MAC/C,QAAQ;AAAA,IACV,CAAC;AACD,UAAM,WAAW,qBAAqB,QAAQ,EAAE,IAAI,OAAO;AAC3D,cAAU,MAAM,SAAS,QAAQ,CAAC;AAClC,UAAM,sBAAsB,MAAM,gBAAgB,OAAO,EAAE,KAAK,MAAM,aAAa,QAAQ,CAAC;AAC5F,UAAM,eAAe,qBAAqB,QAAQ,EAAE,IAAI,mBAAmB;AAC3E,MAAE,KAAK,OAAK;AACV,YAAM;AACN,aAAO,cAAc,WAAW,UAAU,aAAa,MAAM,CAAC,IAAI,SAAS,MAAM,CAAC;AAAA,IACpF;AAAA,EACF;AACA,WAAS,oBAAoB,SAAS,iBAAiB,CAAC;AACxD,SAAO;AACT;AACA,SAAS,OAAO,MAAM;AACpB,QAAM,oBAAoB,cAAc,WAAW;AACnD,OAAK,oBAAoB,KAAK,SAAS,KAAK,WAAW,EAAG;AAC1D,OAAK,oBAAoB,KAAK,SAAS,KAAK,WAAW,QAAS,QAAO,aAAa,IAAI;AACxF,MAAI,KAAK,YAAY,QAAQ,KAAK,SAAS,UAAU,EAAG,QAAO,KAAK,SAAS,QAAQ,KAAK,IAAI;AAC9F,QAAM,YAAY,CAAC,IAAI;AACvB,UAAQ,OAAO,KAAK,WAAW,CAAC,KAAK,aAAa,KAAK,YAAY,YAAY;AAC7E,QAAI,qBAAqB,WAAW,SAAS,IAAI,IAAI,EAAG;AACxD,QAAI,oBAAoB,KAAK,SAAS,KAAK,MAAO,WAAU,KAAK,IAAI;AAAA,EACvE;AACA,WAAS,IAAI,UAAU,SAAS,GAAG,KAAK,GAAG,KAAK;AAC9C,WAAO,UAAU,CAAC;AAClB,QAAI,mBAAmB;AACrB,UAAI,MAAM,MACR,OAAO,UAAU,IAAI,CAAC;AACxB,cAAQ,MAAM,IAAI,UAAU,QAAQ,MAAM;AACxC,YAAI,WAAW,SAAS,IAAI,GAAG,EAAG;AAAA,MACpC;AAAA,IACF;AACA,SAAK,oBAAoB,KAAK,SAAS,KAAK,WAAW,OAAO;AAC5D,wBAAkB,IAAI;AAAA,IACxB,YAAY,oBAAoB,KAAK,SAAS,KAAK,WAAW,SAAS;AACrE,YAAM,UAAU;AAChB,gBAAU;AACV,iBAAW,MAAM,aAAa,MAAM,UAAU,CAAC,CAAC,GAAG,KAAK;AACxD,gBAAU;AAAA,IACZ;AAAA,EACF;AACF;AACA,SAAS,WAAW,IAAI,MAAM;AAC5B,MAAI,QAAS,QAAO,GAAG;AACvB,MAAI,OAAO;AACX,MAAI,CAAC,KAAM,WAAU,CAAC;AACtB,MAAI,QAAS,QAAO;AAAA,MAAU,WAAU,CAAC;AACzC;AACA,MAAI;AACF,UAAM,MAAM,GAAG;AACf,oBAAgB,IAAI;AACpB,WAAO;AAAA,EACT,SAAS,KAAK;AACZ,QAAI,CAAC,KAAM,WAAU;AACrB,cAAU;AACV,gBAAY,GAAG;AAAA,EACjB;AACF;AACA,SAAS,gBAAgB,MAAM;AAC7B,MAAI,SAAS;AACX,QAAI,aAAa,cAAc,WAAW,QAAS,eAAc,OAAO;AAAA,QAAO,UAAS,OAAO;AAC/F,cAAU;AAAA,EACZ;AACA,MAAI,KAAM;AACV,MAAI;AACJ,MAAI,YAAY;AACd,QAAI,CAAC,WAAW,SAAS,QAAQ,CAAC,WAAW,MAAM,MAAM;AACvD,YAAM,UAAU,WAAW;AAC3B,YAAM,WAAW,WAAW;AAC5B,cAAQ,KAAK,MAAM,SAAS,WAAW,OAAO;AAC9C,YAAM,WAAW;AACjB,iBAAWC,MAAK,SAAS;AACvB,oBAAYA,OAAMA,GAAE,QAAQA,GAAE;AAC9B,eAAOA,GAAE;AAAA,MACX;AACA,mBAAa;AACb,iBAAW,MAAM;AACf,mBAAW,KAAK,SAAU,WAAU,CAAC;AACrC,mBAAW,KAAK,SAAS;AACvB,YAAE,QAAQ,EAAE;AACZ,cAAI,EAAE,OAAO;AACX,qBAAS,IAAI,GAAG,MAAM,EAAE,MAAM,QAAQ,IAAI,KAAK,IAAK,WAAU,EAAE,MAAM,CAAC,CAAC;AAAA,UAC1E;AACA,cAAI,EAAE,OAAQ,GAAE,QAAQ,EAAE;AAC1B,iBAAO,EAAE;AACT,iBAAO,EAAE;AACT,YAAE,SAAS;AAAA,QACb;AACA,wBAAgB,KAAK;AAAA,MACvB,GAAG,KAAK;AAAA,IACV,WAAW,WAAW,SAAS;AAC7B,iBAAW,UAAU;AACrB,iBAAW,QAAQ,KAAK,MAAM,WAAW,SAAS,OAAO;AACzD,gBAAU;AACV,sBAAgB,IAAI;AACpB;AAAA,IACF;AAAA,EACF;AACA,QAAM,IAAI;AACV,YAAU;AACV,MAAI,EAAE,OAAQ,YAAW,MAAM,WAAW,CAAC,GAAG,KAAK;AAAA,MAAO,UAAS,eAAe,SAAS,YAAY;AACvG,MAAI,IAAK,KAAI;AACf;AACA,SAAS,SAAS,OAAO;AACvB,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAK,QAAO,MAAM,CAAC,CAAC;AACxD;AACA,SAAS,cAAc,OAAO;AAC5B,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,UAAM,OAAO,MAAM,CAAC;AACpB,UAAM,QAAQ,WAAW;AACzB,QAAI,CAAC,MAAM,IAAI,IAAI,GAAG;AACpB,YAAM,IAAI,IAAI;AACd,gBAAU,MAAM;AACd,cAAM,OAAO,IAAI;AACjB,mBAAW,MAAM;AACf,qBAAW,UAAU;AACrB,iBAAO,IAAI;AAAA,QACb,GAAG,KAAK;AACR,uBAAe,WAAW,UAAU;AAAA,MACtC,CAAC;AAAA,IACH;AAAA,EACF;AACF;AACA,SAAS,eAAe,OAAO;AAC7B,MAAI,GACF,aAAa;AACf,OAAK,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACjC,UAAM,IAAI,MAAM,CAAC;AACjB,QAAI,CAAC,EAAE,KAAM,QAAO,CAAC;AAAA,QAAO,OAAM,YAAY,IAAI;AAAA,EACpD;AACA,MAAI,aAAa,SAAS;AACxB,QAAI,aAAa,OAAO;AACtB,mBAAa,YAAY,aAAa,UAAU,CAAC;AACjD,mBAAa,QAAQ,KAAK,GAAG,MAAM,MAAM,GAAG,UAAU,CAAC;AACvD;AAAA,IACF;AACA,sBAAkB;AAAA,EACpB;AACA,MAAI,aAAa,YAAY,aAAa,QAAQ,CAAC,aAAa,QAAQ;AACtE,YAAQ,CAAC,GAAG,aAAa,SAAS,GAAG,KAAK;AAC1C,kBAAc,aAAa,QAAQ;AACnC,WAAO,aAAa;AAAA,EACtB;AACA,OAAK,IAAI,GAAG,IAAI,YAAY,IAAK,QAAO,MAAM,CAAC,CAAC;AAClD;AACA,SAAS,aAAa,MAAM,QAAQ;AAClC,QAAM,oBAAoB,cAAc,WAAW;AACnD,MAAI,kBAAmB,MAAK,SAAS;AAAA,MAAO,MAAK,QAAQ;AACzD,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,QAAQ,KAAK,GAAG;AAC/C,UAAM,SAAS,KAAK,QAAQ,CAAC;AAC7B,QAAI,OAAO,SAAS;AAClB,YAAM,QAAQ,oBAAoB,OAAO,SAAS,OAAO;AACzD,UAAI,UAAU,OAAO;AACnB,YAAI,WAAW,WAAW,CAAC,OAAO,aAAa,OAAO,YAAY,WAAY,QAAO,MAAM;AAAA,MAC7F,WAAW,UAAU,QAAS,cAAa,QAAQ,MAAM;AAAA,IAC3D;AAAA,EACF;AACF;AACA,SAAS,eAAe,MAAM;AAC5B,QAAM,oBAAoB,cAAc,WAAW;AACnD,WAAS,IAAI,GAAG,IAAI,KAAK,UAAU,QAAQ,KAAK,GAAG;AACjD,UAAM,IAAI,KAAK,UAAU,CAAC;AAC1B,QAAI,oBAAoB,CAAC,EAAE,SAAS,CAAC,EAAE,OAAO;AAC5C,UAAI,kBAAmB,GAAE,SAAS;AAAA,UAAa,GAAE,QAAQ;AACzD,UAAI,EAAE,KAAM,SAAQ,KAAK,CAAC;AAAA,UAAO,SAAQ,KAAK,CAAC;AAC/C,QAAE,aAAa,eAAe,CAAC;AAAA,IACjC;AAAA,EACF;AACF;AACA,SAAS,UAAU,MAAM;AACvB,MAAI;AACJ,MAAI,KAAK,SAAS;AAChB,WAAO,KAAK,QAAQ,QAAQ;AAC1B,YAAM,SAAS,KAAK,QAAQ,IAAI,GAC9B,QAAQ,KAAK,YAAY,IAAI,GAC7B,MAAM,OAAO;AACf,UAAI,OAAO,IAAI,QAAQ;AACrB,cAAM,IAAI,IAAI,IAAI,GAChB,IAAI,OAAO,cAAc,IAAI;AAC/B,YAAI,QAAQ,IAAI,QAAQ;AACtB,YAAE,YAAY,CAAC,IAAI;AACnB,cAAI,KAAK,IAAI;AACb,iBAAO,cAAc,KAAK,IAAI;AAAA,QAChC;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,MAAI,KAAK,QAAQ;AACf,SAAK,IAAI,KAAK,OAAO,SAAS,GAAG,KAAK,GAAG,IAAK,WAAU,KAAK,OAAO,CAAC,CAAC;AACtE,WAAO,KAAK;AAAA,EACd;AACA,MAAI,cAAc,WAAW,WAAW,KAAK,MAAM;AACjD,UAAM,MAAM,IAAI;AAAA,EAClB,WAAW,KAAK,OAAO;AACrB,SAAK,IAAI,KAAK,MAAM,SAAS,GAAG,KAAK,GAAG,IAAK,WAAU,KAAK,MAAM,CAAC,CAAC;AACpE,SAAK,QAAQ;AAAA,EACf;AACA,MAAI,KAAK,UAAU;AACjB,SAAK,IAAI,KAAK,SAAS,SAAS,GAAG,KAAK,GAAG,IAAK,MAAK,SAAS,CAAC,EAAE;AACjE,SAAK,WAAW;AAAA,EAClB;AACA,MAAI,cAAc,WAAW,QAAS,MAAK,SAAS;AAAA,MAAO,MAAK,QAAQ;AACxE,SAAO,KAAK;AACd;AACA,SAAS,MAAM,MAAM,KAAK;AACxB,MAAI,CAAC,KAAK;AACR,SAAK,SAAS;AACd,eAAW,SAAS,IAAI,IAAI;AAAA,EAC9B;AACA,MAAI,KAAK,OAAO;AACd,aAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,IAAK,OAAM,KAAK,MAAM,CAAC,CAAC;AAAA,EACjE;AACF;AACA,SAAS,UAAU,KAAK;AACtB,MAAI,eAAe,MAAO,QAAO;AACjC,SAAO,IAAI,MAAM,OAAO,QAAQ,WAAW,MAAM,iBAAiB;AAAA,IAChE,OAAO;AAAA,EACT,CAAC;AACH;AACA,SAAS,UAAU,KAAK,KAAK,OAAO;AAClC,MAAI;AACF,eAAW,KAAK,IAAK,GAAE,GAAG;AAAA,EAC5B,SAAS,GAAG;AACV,gBAAY,GAAG,SAAS,MAAM,SAAS,IAAI;AAAA,EAC7C;AACF;AACA,SAAS,YAAY,KAAK,QAAQ,OAAO;AACvC,QAAM,MAAM,SAAS,SAAS,MAAM,WAAW,MAAM,QAAQ,KAAK;AAClE,QAAM,QAAQ,UAAU,GAAG;AAC3B,MAAI,CAAC,IAAK,OAAM;AAChB,MAAI,QAAS,SAAQ,KAAK;AAAA,IACxB,KAAK;AACH,gBAAU,OAAO,KAAK,KAAK;AAAA,IAC7B;AAAA,IACA,OAAO;AAAA,EACT,CAAC;AAAA,MAAO,WAAU,OAAO,KAAK,KAAK;AACrC;AACA,SAAS,gBAAgBF,WAAU;AACjC,MAAI,OAAOA,cAAa,cAAc,CAACA,UAAS,OAAQ,QAAO,gBAAgBA,UAAS,CAAC;AACzF,MAAI,MAAM,QAAQA,SAAQ,GAAG;AAC3B,UAAM,UAAU,CAAC;AACjB,aAAS,IAAI,GAAG,IAAIA,UAAS,QAAQ,KAAK;AACxC,YAAM,SAAS,gBAAgBA,UAAS,CAAC,CAAC;AAC1C,YAAM,QAAQ,MAAM,IAAI,QAAQ,KAAK,MAAM,SAAS,MAAM,IAAI,QAAQ,KAAK,MAAM;AAAA,IACnF;AACA,WAAO;AAAA,EACT;AACA,SAAOA;AACT;AACA,SAAS,eAAe,IAAI,SAAS;AACnC,SAAO,SAAS,SAAS,OAAO;AAC9B,QAAI;AACJ,uBAAmB,MAAM,MAAM,QAAQ,MAAM;AAC3C,YAAM,UAAU;AAAA,QACd,GAAG,MAAM;AAAA,QACT,CAAC,EAAE,GAAG,MAAM;AAAA,MACd;AACA,aAAO,SAAS,MAAM,MAAM,QAAQ;AAAA,IACtC,CAAC,GAAG,QAAW,OAAO;AACtB,WAAO;AAAA,EACT;AACF;AACA,SAAS,QAAQ,IAAI;AACnB,YAAU,QAAQ,OAAO,OAAO;AAChC,MAAI,UAAU,KAAM,SAAQ,KAAK,6EAA6E;AAAA,WAAW,MAAM,YAAY,QAAQ,CAAC,MAAM,QAAQ,KAAK,GAAG;AACxK,UAAM,UAAU;AAAA,MACd,GAAG,MAAM;AAAA,MACT,CAAC,KAAK,GAAG,CAAC,EAAE;AAAA,IACd;AACA,kBAAc,OAAO,OAAO,CAAC,EAAE,CAAC;AAAA,EAClC,MAAO,OAAM,QAAQ,KAAK,EAAE,KAAK,EAAE;AACrC;AACA,SAAS,cAAc,GAAG,KAAK,OAAO;AACpC,MAAI,EAAE,OAAO;AACX,aAAS,IAAI,GAAG,IAAI,EAAE,MAAM,QAAQ,KAAK;AACvC,UAAI,EAAE,MAAM,CAAC,EAAE,YAAY,EAAE,QAAS,eAAc,EAAE,MAAM,CAAC,GAAG,KAAK,KAAK;AAC1E,UAAI,CAAC,EAAE,MAAM,CAAC,EAAE,SAAS;AACvB,UAAE,MAAM,CAAC,EAAE,UAAU,EAAE;AACvB,sBAAc,EAAE,MAAM,CAAC,GAAG,KAAK,KAAK;AAAA,MACtC,WAAW,CAAC,EAAE,MAAM,CAAC,EAAE,QAAQ,GAAG,GAAG;AACnC,UAAE,MAAM,CAAC,EAAE,QAAQ,GAAG,IAAI;AAC1B,sBAAc,EAAE,MAAM,CAAC,GAAG,KAAK,KAAK;AAAA,MACtC;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAS,WAAW,OAAO;AACzB,SAAO;AAAA,IACL,UAAU,UAAU;AAClB,UAAI,EAAE,oBAAoB,WAAW,YAAY,MAAM;AACrD,cAAM,IAAI,UAAU,wCAAwC;AAAA,MAC9D;AACA,YAAM,UAAU,OAAO,aAAa,aAAa,WAAW,SAAS,QAAQ,SAAS,KAAK,KAAK,QAAQ;AACxG,UAAI,CAAC,SAAS;AACZ,eAAO;AAAA,UACL,cAAc;AAAA,UAAC;AAAA,QACjB;AAAA,MACF;AACA,YAAMG,WAAU,WAAW,cAAY;AACrC,qBAAa,MAAM;AACjB,gBAAM,IAAI,MAAM;AAChB,kBAAQ,MAAM,QAAQ,CAAC,CAAC;AAAA,QAC1B,CAAC;AACD,eAAO;AAAA,MACT,CAAC;AACD,UAAI,SAAS,EAAG,WAAUA,QAAO;AACjC,aAAO;AAAA,QACL,cAAc;AACZ,UAAAA,SAAQ;AAAA,QACV;AAAA,MACF;AAAA,IACF;AAAA,IACA,CAAC,OAAO,cAAc,cAAc,IAAI;AACtC,aAAO;AAAA,IACT;AAAA,EACF;AACF;AACA,SAAS,KAAK,UAAU,cAAc,QAAW;AAC/C,QAAM,CAAC,GAAG,GAAG,IAAI,aAAa,aAAa;AAAA,IACzC,QAAQ;AAAA,EACV,CAAC;AACD,MAAI,eAAe,UAAU;AAC3B,UAAM,QAAQ,SAAS,UAAU,OAAK,IAAI,MAAM,CAAC,CAAC;AAClD,cAAU,MAAM,iBAAiB,QAAQ,MAAM,YAAY,IAAI,MAAM,CAAC;AAAA,EACxE,OAAO;AACL,UAAM,QAAQ,SAAS,GAAG;AAC1B,cAAU,KAAK;AAAA,EACjB;AACA,SAAO;AACT;AAEA,IAAM,WAAW,OAAO,UAAU;AAClC,SAAS,QAAQ,GAAG;AAClB,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,IAAK,GAAE,CAAC,EAAE;AAC1C;AACA,SAAS,SAAS,MAAM,OAAO,UAAU,CAAC,GAAG;AAC3C,MAAI,QAAQ,CAAC,GACX,SAAS,CAAC,GACV,YAAY,CAAC,GACb,MAAM,GACN,UAAU,MAAM,SAAS,IAAI,CAAC,IAAI;AACpC,YAAU,MAAM,QAAQ,SAAS,CAAC;AAClC,SAAO,MAAM;AACX,QAAI,WAAW,KAAK,KAAK,CAAC,GACxB,SAAS,SAAS,QAClB,GACA;AACF,aAAS,MAAM;AACf,WAAO,QAAQ,MAAM;AACnB,UAAI,YAAY,gBAAgB,MAAM,eAAe,aAAa,OAAO,KAAK,QAAQ;AACtF,UAAI,WAAW,GAAG;AAChB,YAAI,QAAQ,GAAG;AACb,kBAAQ,SAAS;AACjB,sBAAY,CAAC;AACb,kBAAQ,CAAC;AACT,mBAAS,CAAC;AACV,gBAAM;AACN,sBAAY,UAAU,CAAC;AAAA,QACzB;AACA,YAAI,QAAQ,UAAU;AACpB,kBAAQ,CAAC,QAAQ;AACjB,iBAAO,CAAC,IAAI,WAAW,cAAY;AACjC,sBAAU,CAAC,IAAI;AACf,mBAAO,QAAQ,SAAS;AAAA,UAC1B,CAAC;AACD,gBAAM;AAAA,QACR;AAAA,MACF,WACS,QAAQ,GAAG;AAClB,iBAAS,IAAI,MAAM,MAAM;AACzB,aAAK,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC3B,gBAAM,CAAC,IAAI,SAAS,CAAC;AACrB,iBAAO,CAAC,IAAI,WAAW,MAAM;AAAA,QAC/B;AACA,cAAM;AAAA,MACR,OAAO;AACL,eAAO,IAAI,MAAM,MAAM;AACvB,wBAAgB,IAAI,MAAM,MAAM;AAChC,oBAAY,cAAc,IAAI,MAAM,MAAM;AAC1C,aAAK,QAAQ,GAAG,MAAM,KAAK,IAAI,KAAK,MAAM,GAAG,QAAQ,OAAO,MAAM,KAAK,MAAM,SAAS,KAAK,GAAG,QAAQ;AACtG,aAAK,MAAM,MAAM,GAAG,SAAS,SAAS,GAAG,OAAO,SAAS,UAAU,SAAS,MAAM,GAAG,MAAM,SAAS,MAAM,GAAG,OAAO,UAAU;AAC5H,eAAK,MAAM,IAAI,OAAO,GAAG;AACzB,wBAAc,MAAM,IAAI,UAAU,GAAG;AACrC,sBAAY,YAAY,MAAM,IAAI,QAAQ,GAAG;AAAA,QAC/C;AACA,qBAAa,oBAAI,IAAI;AACrB,yBAAiB,IAAI,MAAM,SAAS,CAAC;AACrC,aAAK,IAAI,QAAQ,KAAK,OAAO,KAAK;AAChC,iBAAO,SAAS,CAAC;AACjB,cAAI,WAAW,IAAI,IAAI;AACvB,yBAAe,CAAC,IAAI,MAAM,SAAY,KAAK;AAC3C,qBAAW,IAAI,MAAM,CAAC;AAAA,QACxB;AACA,aAAK,IAAI,OAAO,KAAK,KAAK,KAAK;AAC7B,iBAAO,MAAM,CAAC;AACd,cAAI,WAAW,IAAI,IAAI;AACvB,cAAI,MAAM,UAAa,MAAM,IAAI;AAC/B,iBAAK,CAAC,IAAI,OAAO,CAAC;AAClB,0BAAc,CAAC,IAAI,UAAU,CAAC;AAC9B,wBAAY,YAAY,CAAC,IAAI,QAAQ,CAAC;AACtC,gBAAI,eAAe,CAAC;AACpB,uBAAW,IAAI,MAAM,CAAC;AAAA,UACxB,MAAO,WAAU,CAAC,EAAE;AAAA,QACtB;AACA,aAAK,IAAI,OAAO,IAAI,QAAQ,KAAK;AAC/B,cAAI,KAAK,MAAM;AACb,mBAAO,CAAC,IAAI,KAAK,CAAC;AAClB,sBAAU,CAAC,IAAI,cAAc,CAAC;AAC9B,gBAAI,SAAS;AACX,sBAAQ,CAAC,IAAI,YAAY,CAAC;AAC1B,sBAAQ,CAAC,EAAE,CAAC;AAAA,YACd;AAAA,UACF,MAAO,QAAO,CAAC,IAAI,WAAW,MAAM;AAAA,QACtC;AACA,iBAAS,OAAO,MAAM,GAAG,MAAM,MAAM;AACrC,gBAAQ,SAAS,MAAM,CAAC;AAAA,MAC1B;AACA,aAAO;AAAA,IACT,CAAC;AACD,aAAS,OAAO,UAAU;AACxB,gBAAU,CAAC,IAAI;AACf,UAAI,SAAS;AACX,cAAM,CAAC,GAAG,GAAG,IAAI,aAAa,GAAG;AAAA,UAC/B,MAAM;AAAA,QACR,CAAC;AACD,gBAAQ,CAAC,IAAI;AACb,eAAO,MAAM,SAAS,CAAC,GAAG,CAAC;AAAA,MAC7B;AACA,aAAO,MAAM,SAAS,CAAC,CAAC;AAAA,IAC1B;AAAA,EACF;AACF;AACA,SAAS,WAAW,MAAM,OAAO,UAAU,CAAC,GAAG;AAC7C,MAAI,QAAQ,CAAC,GACX,SAAS,CAAC,GACV,YAAY,CAAC,GACb,UAAU,CAAC,GACX,MAAM,GACN;AACF,YAAU,MAAM,QAAQ,SAAS,CAAC;AAClC,SAAO,MAAM;AACX,UAAM,WAAW,KAAK,KAAK,CAAC,GAC1B,SAAS,SAAS;AACpB,aAAS,MAAM;AACf,WAAO,QAAQ,MAAM;AACnB,UAAI,WAAW,GAAG;AAChB,YAAI,QAAQ,GAAG;AACb,kBAAQ,SAAS;AACjB,sBAAY,CAAC;AACb,kBAAQ,CAAC;AACT,mBAAS,CAAC;AACV,gBAAM;AACN,oBAAU,CAAC;AAAA,QACb;AACA,YAAI,QAAQ,UAAU;AACpB,kBAAQ,CAAC,QAAQ;AACjB,iBAAO,CAAC,IAAI,WAAW,cAAY;AACjC,sBAAU,CAAC,IAAI;AACf,mBAAO,QAAQ,SAAS;AAAA,UAC1B,CAAC;AACD,gBAAM;AAAA,QACR;AACA,eAAO;AAAA,MACT;AACA,UAAI,MAAM,CAAC,MAAM,UAAU;AACzB,kBAAU,CAAC,EAAE;AACb,oBAAY,CAAC;AACb,gBAAQ,CAAC;AACT,iBAAS,CAAC;AACV,cAAM;AAAA,MACR;AACA,WAAK,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC3B,YAAI,IAAI,MAAM,UAAU,MAAM,CAAC,MAAM,SAAS,CAAC,GAAG;AAChD,kBAAQ,CAAC,EAAE,MAAM,SAAS,CAAC,CAAC;AAAA,QAC9B,WAAW,KAAK,MAAM,QAAQ;AAC5B,iBAAO,CAAC,IAAI,WAAW,MAAM;AAAA,QAC/B;AAAA,MACF;AACA,aAAO,IAAI,MAAM,QAAQ,KAAK;AAC5B,kBAAU,CAAC,EAAE;AAAA,MACf;AACA,YAAM,QAAQ,SAAS,UAAU,SAAS;AAC1C,cAAQ,SAAS,MAAM,CAAC;AACxB,aAAO,SAAS,OAAO,MAAM,GAAG,GAAG;AAAA,IACrC,CAAC;AACD,aAAS,OAAO,UAAU;AACxB,gBAAU,CAAC,IAAI;AACf,YAAM,CAAC,GAAG,GAAG,IAAI,aAAa,SAAS,CAAC,GAAG;AAAA,QACzC,MAAM;AAAA,MACR,CAAC;AACD,cAAQ,CAAC,IAAI;AACb,aAAO,MAAM,GAAG,CAAC;AAAA,IACnB;AAAA,EACF;AACF;AAEA,IAAI,mBAAmB;AACvB,SAAS,kBAAkB;AACzB,qBAAmB;AACrB;AACA,SAAS,gBAAgB,MAAM,OAAO;AACpC,MAAI,kBAAkB;AACpB,QAAI,aAAa,SAAS;AACxB,YAAM,IAAI,aAAa;AACvB,wBAAkB,mBAAmB,CAAC;AACtC,YAAM,IAAI,aAAa,MAAM,SAAS,CAAC,CAAC;AACxC,wBAAkB,CAAC;AACnB,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO,aAAa,MAAM,SAAS,CAAC,CAAC;AACvC;AACA,SAAS,SAAS;AAChB,SAAO;AACT;AACA,IAAM,YAAY;AAAA,EAChB,IAAI,GAAG,UAAU,UAAU;AACzB,QAAI,aAAa,OAAQ,QAAO;AAChC,WAAO,EAAE,IAAI,QAAQ;AAAA,EACvB;AAAA,EACA,IAAI,GAAG,UAAU;AACf,QAAI,aAAa,OAAQ,QAAO;AAChC,WAAO,EAAE,IAAI,QAAQ;AAAA,EACvB;AAAA,EACA,KAAK;AAAA,EACL,gBAAgB;AAAA,EAChB,yBAAyB,GAAG,UAAU;AACpC,WAAO;AAAA,MACL,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,MAAM;AACJ,eAAO,EAAE,IAAI,QAAQ;AAAA,MACvB;AAAA,MACA,KAAK;AAAA,MACL,gBAAgB;AAAA,IAClB;AAAA,EACF;AAAA,EACA,QAAQ,GAAG;AACT,WAAO,EAAE,KAAK;AAAA,EAChB;AACF;AACA,SAAS,cAAc,GAAG;AACxB,SAAO,EAAE,IAAI,OAAO,MAAM,aAAa,EAAE,IAAI,KAAK,CAAC,IAAI;AACzD;AACA,SAAS,iBAAiB;AACxB,WAAS,IAAI,GAAG,SAAS,KAAK,QAAQ,IAAI,QAAQ,EAAE,GAAG;AACrD,UAAM,IAAI,KAAK,CAAC,EAAE;AAClB,QAAI,MAAM,OAAW,QAAO;AAAA,EAC9B;AACF;AACA,SAAS,cAAc,SAAS;AAC9B,MAAI,QAAQ;AACZ,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,UAAM,IAAI,QAAQ,CAAC;AACnB,YAAQ,SAAS,CAAC,CAAC,KAAK,UAAU;AAClC,YAAQ,CAAC,IAAI,OAAO,MAAM,cAAc,QAAQ,MAAM,WAAW,CAAC,KAAK;AAAA,EACzE;AACA,MAAI,kBAAkB,OAAO;AAC3B,WAAO,IAAI,MAAM;AAAA,MACf,IAAI,UAAU;AACZ,iBAAS,IAAI,QAAQ,SAAS,GAAG,KAAK,GAAG,KAAK;AAC5C,gBAAM,IAAI,cAAc,QAAQ,CAAC,CAAC,EAAE,QAAQ;AAC5C,cAAI,MAAM,OAAW,QAAO;AAAA,QAC9B;AAAA,MACF;AAAA,MACA,IAAI,UAAU;AACZ,iBAAS,IAAI,QAAQ,SAAS,GAAG,KAAK,GAAG,KAAK;AAC5C,cAAI,YAAY,cAAc,QAAQ,CAAC,CAAC,EAAG,QAAO;AAAA,QACpD;AACA,eAAO;AAAA,MACT;AAAA,MACA,OAAO;AACL,cAAM,OAAO,CAAC;AACd,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,IAAK,MAAK,KAAK,GAAG,OAAO,KAAK,cAAc,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC5F,eAAO,CAAC,GAAG,IAAI,IAAI,IAAI,CAAC;AAAA,MAC1B;AAAA,IACF,GAAG,SAAS;AAAA,EACd;AACA,QAAM,aAAa,CAAC;AACpB,QAAM,UAAU,uBAAO,OAAO,IAAI;AAClC,WAAS,IAAI,QAAQ,SAAS,GAAG,KAAK,GAAG,KAAK;AAC5C,UAAM,SAAS,QAAQ,CAAC;AACxB,QAAI,CAAC,OAAQ;AACb,UAAM,aAAa,OAAO,oBAAoB,MAAM;AACpD,aAASC,KAAI,WAAW,SAAS,GAAGA,MAAK,GAAGA,MAAK;AAC/C,YAAM,MAAM,WAAWA,EAAC;AACxB,UAAI,QAAQ,eAAe,QAAQ,cAAe;AAClD,YAAM,OAAO,OAAO,yBAAyB,QAAQ,GAAG;AACxD,UAAI,CAAC,QAAQ,GAAG,GAAG;AACjB,gBAAQ,GAAG,IAAI,KAAK,MAAM;AAAA,UACxB,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,KAAK,eAAe,KAAK,WAAW,GAAG,IAAI,CAAC,KAAK,IAAI,KAAK,MAAM,CAAC,CAAC;AAAA,QACpE,IAAI,KAAK,UAAU,SAAY,OAAO;AAAA,MACxC,OAAO;AACL,cAAMC,WAAU,WAAW,GAAG;AAC9B,YAAIA,UAAS;AACX,cAAI,KAAK,IAAK,CAAAA,SAAQ,KAAK,KAAK,IAAI,KAAK,MAAM,CAAC;AAAA,mBAAW,KAAK,UAAU,OAAW,CAAAA,SAAQ,KAAK,MAAM,KAAK,KAAK;AAAA,QACpH;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,QAAM,SAAS,CAAC;AAChB,QAAM,cAAc,OAAO,KAAK,OAAO;AACvC,WAAS,IAAI,YAAY,SAAS,GAAG,KAAK,GAAG,KAAK;AAChD,UAAM,MAAM,YAAY,CAAC,GACvB,OAAO,QAAQ,GAAG;AACpB,QAAI,QAAQ,KAAK,IAAK,QAAO,eAAe,QAAQ,KAAK,IAAI;AAAA,QAAO,QAAO,GAAG,IAAI,OAAO,KAAK,QAAQ;AAAA,EACxG;AACA,SAAO;AACT;AACA,SAAS,WAAW,UAAU,MAAM;AAClC,MAAI,kBAAkB,UAAU,OAAO;AACrC,UAAM,UAAU,IAAI,IAAI,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,KAAK,CAAC,CAAC;AAC/D,UAAM,MAAM,KAAK,IAAI,OAAK;AACxB,aAAO,IAAI,MAAM;AAAA,QACf,IAAI,UAAU;AACZ,iBAAO,EAAE,SAAS,QAAQ,IAAI,MAAM,QAAQ,IAAI;AAAA,QAClD;AAAA,QACA,IAAI,UAAU;AACZ,iBAAO,EAAE,SAAS,QAAQ,KAAK,YAAY;AAAA,QAC7C;AAAA,QACA,OAAO;AACL,iBAAO,EAAE,OAAO,cAAY,YAAY,KAAK;AAAA,QAC/C;AAAA,MACF,GAAG,SAAS;AAAA,IACd,CAAC;AACD,QAAI,KAAK,IAAI,MAAM;AAAA,MACjB,IAAI,UAAU;AACZ,eAAO,QAAQ,IAAI,QAAQ,IAAI,SAAY,MAAM,QAAQ;AAAA,MAC3D;AAAA,MACA,IAAI,UAAU;AACZ,eAAO,QAAQ,IAAI,QAAQ,IAAI,QAAQ,YAAY;AAAA,MACrD;AAAA,MACA,OAAO;AACL,eAAO,OAAO,KAAK,KAAK,EAAE,OAAO,OAAK,CAAC,QAAQ,IAAI,CAAC,CAAC;AAAA,MACvD;AAAA,IACF,GAAG,SAAS,CAAC;AACb,WAAO;AAAA,EACT;AACA,QAAM,cAAc,CAAC;AACrB,QAAM,UAAU,KAAK,IAAI,OAAO,CAAC,EAAE;AACnC,aAAW,YAAY,OAAO,oBAAoB,KAAK,GAAG;AACxD,UAAM,OAAO,OAAO,yBAAyB,OAAO,QAAQ;AAC5D,UAAM,gBAAgB,CAAC,KAAK,OAAO,CAAC,KAAK,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK;AACzF,QAAI,UAAU;AACd,QAAI,cAAc;AAClB,eAAW,KAAK,MAAM;AACpB,UAAI,EAAE,SAAS,QAAQ,GAAG;AACxB,kBAAU;AACV,wBAAgB,QAAQ,WAAW,EAAE,QAAQ,IAAI,KAAK,QAAQ,OAAO,eAAe,QAAQ,WAAW,GAAG,UAAU,IAAI;AAAA,MAC1H;AACA,QAAE;AAAA,IACJ;AACA,QAAI,CAAC,SAAS;AACZ,sBAAgB,YAAY,QAAQ,IAAI,KAAK,QAAQ,OAAO,eAAe,aAAa,UAAU,IAAI;AAAA,IACxG;AAAA,EACF;AACA,SAAO,CAAC,GAAG,SAAS,WAAW;AACjC;AACA,SAAS,KAAK,IAAI;AAChB,MAAI;AACJ,MAAI;AACJ,QAAM,OAAO,WAAS;AACpB,UAAM,MAAM,aAAa;AACzB,QAAI,KAAK;AACP,YAAM,CAAC,GAAG,GAAG,IAAI,aAAa;AAC9B,mBAAa,UAAU,aAAa,QAAQ;AAC5C,mBAAa;AACb,OAAC,MAAM,IAAI,GAAG,IAAI,KAAK,SAAO;AAC5B,SAAC,aAAa,QAAQ,kBAAkB,GAAG;AAC3C,qBAAa;AACb,YAAI,MAAM,IAAI,OAAO;AACrB,0BAAkB;AAAA,MACpB,CAAC;AACD,aAAO;AAAA,IACT,WAAW,CAAC,MAAM;AAChB,YAAM,CAAC,CAAC,IAAI,eAAe,OAAO,MAAM,IAAI,GAAG,IAAI,KAAK,SAAO,IAAI,OAAO,CAAC;AAC3E,aAAO;AAAA,IACT;AACA,QAAI;AACJ,WAAO,WAAW,OAAO,OAAO,KAAK,KAAK,QAAQ,MAAM;AACtD,UAAI,OAAQ,QAAO,OAAO,MAAM;AAAA,QAC9B,CAAC,QAAQ,GAAG;AAAA,MACd,CAAC;AACD,UAAI,CAAC,OAAO,aAAa,KAAM,QAAO,KAAK,KAAK;AAChD,YAAM,IAAI,aAAa;AACvB,wBAAkB,GAAG;AACrB,YAAM,IAAI,KAAK,KAAK;AACpB,wBAAkB,CAAC;AACnB,aAAO;AAAA,IACT,CAAC,IAAI,EAAE;AAAA,EACT;AACA,OAAK,UAAU,MAAM,OAAO,IAAI,GAAG,GAAG,KAAK,SAAO,OAAO,MAAM,IAAI,OAAO,GAAG;AAC7E,SAAO;AACT;AACA,IAAI,UAAU;AACd,SAAS,iBAAiB;AACxB,QAAM,MAAM,aAAa;AACzB,SAAO,MAAM,aAAa,iBAAiB,IAAI,MAAM,SAAS;AAChE;AAEA,IAAM,gBAAgB,UAAQ,4CAA4C,IAAI;AAC9E,SAAS,IAAI,OAAO;AAClB,QAAM,WAAW,cAAc,SAAS;AAAA,IACtC,UAAU,MAAM,MAAM;AAAA,EACxB;AACA,SAAO,WAAW,SAAS,MAAM,MAAM,MAAM,MAAM,UAAU,YAAY,MAAS,GAAG,QAAW;AAAA,IAC9F,MAAM;AAAA,EACR,CAAC;AACH;AACA,SAAS,MAAM,OAAO;AACpB,QAAM,WAAW,cAAc,SAAS;AAAA,IACtC,UAAU,MAAM,MAAM;AAAA,EACxB;AACA,SAAO,WAAW,WAAW,MAAM,MAAM,MAAM,MAAM,UAAU,YAAY,MAAS,GAAG,QAAW;AAAA,IAChG,MAAM;AAAA,EACR,CAAC;AACH;AACA,SAAS,KAAK,OAAO;AACnB,QAAM,QAAQ,MAAM;AACpB,QAAM,iBAAiB,WAAW,MAAM,MAAM,MAAM,QAAW;AAAA,IAC7D,MAAM;AAAA,EACR,CAAE;AACF,QAAM,YAAY,QAAQ,iBAAiB,WAAW,gBAAgB,QAAW;AAAA,IAC/E,QAAQ,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;AAAA,IAC1B,MAAM;AAAA,EACR,CAAE;AACF,SAAO,WAAW,MAAM;AACtB,UAAM,IAAI,UAAU;AACpB,QAAI,GAAG;AACL,YAAM,QAAQ,MAAM;AACpB,YAAM,KAAK,OAAO,UAAU,cAAc,MAAM,SAAS;AACzD,aAAO,KAAK,QAAQ,MAAM,MAAM,QAAQ,IAAI,MAAM;AAChD,YAAI,CAAC,QAAQ,SAAS,EAAG,OAAM,cAAc,MAAM;AACnD,eAAO,eAAe;AAAA,MACxB,CAAC,CAAC,IAAI;AAAA,IACR;AACA,WAAO,MAAM;AAAA,EACf,GAAG,QAAW;AAAA,IACZ,MAAM;AAAA,EACR,CAAE;AACJ;AACA,SAAS,OAAO,OAAO;AACrB,QAAM,MAAM,SAAS,MAAM,MAAM,QAAQ;AACzC,QAAM,aAAa,WAAW,MAAM;AAClC,UAAM,KAAK,IAAI;AACf,UAAM,MAAM,MAAM,QAAQ,EAAE,IAAI,KAAK,CAAC,EAAE;AACxC,QAAI,OAAO,MAAM;AACjB,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,YAAM,QAAQ;AACd,YAAM,KAAK,IAAI,CAAC;AAChB,YAAM,WAAW;AACjB,YAAM,iBAAiB,WAAW,MAAM,SAAS,IAAI,SAAY,GAAG,MAAM,QAAW;AAAA,QACnF,MAAM;AAAA,MACR,CAAE;AACF,YAAM,YAAY,GAAG,QAAQ,iBAAiB,WAAW,gBAAgB,QAAW;AAAA,QAClF,QAAQ,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;AAAA,QAC1B,MAAM;AAAA,MACR,CAAE;AACF,aAAO,MAAM,SAAS,MAAM,UAAU,IAAI,CAAC,OAAO,gBAAgB,EAAE,IAAI;AAAA,IAC1E;AACA,WAAO;AAAA,EACT,CAAC;AACD,SAAO,WAAW,MAAM;AACtB,UAAM,MAAM,WAAW,EAAE;AACzB,QAAI,CAAC,IAAK,QAAO,MAAM;AACvB,UAAM,CAAC,OAAO,gBAAgB,EAAE,IAAI;AACpC,UAAM,QAAQ,GAAG;AACjB,UAAM,KAAK,OAAO,UAAU,cAAc,MAAM,SAAS;AACzD,WAAO,KAAK,QAAQ,MAAM,MAAM,GAAG,QAAQ,eAAe,IAAI,MAAM;AAliDxE;AAmiDM,YAAI,aAAQ,UAAU,EAAE,MAApB,mBAAwB,QAAO,MAAO,OAAM,cAAc,OAAO;AACrE,aAAO,eAAe;AAAA,IACxB,CAAC,CAAC,IAAI;AAAA,EACR,GAAG,QAAW;AAAA,IACZ,MAAM;AAAA,EACR,CAAE;AACJ;AACA,SAAS,MAAM,OAAO;AACpB,SAAO;AACT;AACA,IAAI;AACJ,SAAS,uBAAuB;AAC9B,YAAU,CAAC,GAAG,MAAM,EAAE,QAAQ,QAAM,GAAG,CAAC;AAC1C;AACA,SAAS,cAAc,OAAO;AAC5B,MAAI;AACJ,MAAI,aAAa,WAAW,aAAa,KAAM,OAAM,aAAa,KAAK,aAAa,aAAa,CAAC;AAClG,QAAM,CAAC,SAAS,UAAU,IAAI,aAAa,KAAK;AAAA,IAC9C,MAAM;AAAA,EACR,CAAE;AACF,aAAW,SAAS,oBAAI,IAAI;AAC5B,SAAO,IAAI,UAAU;AACrB,YAAU,MAAM,OAAO,OAAO,UAAU,CAAC;AACzC,SAAO,WAAW,MAAM;AACtB,QAAI;AACJ,QAAI,IAAI,QAAQ,GAAG;AACjB,YAAM,IAAI,MAAM;AAChB,UAAK,OAAO,MAAM,cAAc,EAAE,UAAU,EAAI,SAAQ,MAAM,CAAC;AAC/D,aAAO,OAAO,MAAM,cAAc,EAAE,SAAS,QAAQ,MAAM,EAAE,GAAG,MAAM,WAAW,CAAC,CAAC,IAAI;AAAA,IACzF;AACA,WAAO,WAAW,MAAM,MAAM,UAAU,UAAU;AAAA,EACpD,GAAG,QAAW;AAAA,IACZ,MAAM;AAAA,EACR,CAAE;AACJ;AAEA,IAAM,qBAAqB,CAAC,GAAG,MAAM,EAAE,gBAAgB,EAAE,eAAe,EAAE,iBAAiB,EAAE;AAC7F,IAAM,sBAAqC,cAAc;AACzD,SAAS,aAAa,OAAO;AAC3B,MAAI,CAAC,SAAS,UAAU,IAAI,aAAa,OAAO;AAAA,IAC5C,YAAY;AAAA,EACd,EAAE,GACF;AACF,QAAM,cAAc,WAAW,mBAAmB;AAClD,QAAM,CAAC,UAAU,WAAW,IAAI,aAAa,CAAC,CAAC;AAC/C,MAAI,aAAa;AACf,WAAO,YAAY,SAAS,WAAW,MAAM,QAAQ,EAAE,EAAE,UAAU,CAAC;AAAA,EACtE;AACA,QAAM,WAAW,WAAW,UAAQ;AAClC,UAAM,SAAS,MAAM,aACnB,OAAO,MAAM,MACb;AAAA,MACE,cAAc;AAAA,MACd,eAAe;AAAA,IACjB,IAAI,OAAO,KAAK,IAAI,CAAC,GACrB,MAAM,SAAS,GACf,UAAU,WAAW;AACvB,QAAI,WAAW,YAAY;AACzB,YAAM,MAAM,IAAI,MAAM,CAAAC,gBAAc,CAACA,YAAW,CAAC;AACjD,YAAMC,OAAM,IAAI,IAAI,OAAO;AAAA,QACzB,aAAa,OAAO;AAAA,QACpB;AAAA,MACF,EAAE;AACF,MAAAA,KAAI,aAAa,CAAC;AAClB,aAAOA;AAAA,IACT;AACA,QAAI,OAAO;AACX,QAAI,aAAa,KAAK;AACtB,UAAM,MAAM,CAAC;AACb,aAAS,IAAI,GAAG,MAAM,IAAI,QAAQ,IAAI,KAAK,KAAK;AAC9C,YAAM,IAAI,UAAU,MAAM,IAAI,IAAI,GAChC,IAAI,IAAI,CAAC,EAAE;AACb,UAAI,CAAC,QAAQ,CAAC,GAAG;AACf,YAAI,CAAC,IAAI;AAAA,UACP;AAAA,UACA;AAAA,QACF;AAAA,MACF,OAAO;AACL,cAAM,OAAO,CAAC;AACd,YAAI,KAAM,cAAa;AACvB,YAAI,CAAC,IAAI;AAAA,UACP,aAAa;AAAA,UACb,cAAc,CAAC,QAAQ,QAAQ,SAAS,cAAc,eAAe;AAAA,QACvE;AACA,eAAO;AAAA,MACT;AAAA,IACF;AACA,QAAI,CAAC,KAAM,cAAa;AACxB,QAAI,aAAa;AACjB,WAAO;AAAA,EACT,GAAG;AAAA,IACD,YAAY;AAAA,EACd,CAAC;AACD,aAAW,MAAM,QAAQ;AACzB,SAAO,gBAAgB,oBAAoB,UAAU;AAAA,IACnD,OAAO;AAAA,MACL,UAAU,gBAAc;AACtB,YAAI;AACJ,oBAAY,CAAAC,cAAY;AACtB,kBAAQA,UAAS;AACjB,iBAAO,CAAC,GAAGA,WAAU,UAAU;AAAA,QACjC,CAAC;AACD,eAAO,WAAW,MAAM,SAAS,EAAE,KAAK,GAAG,QAAW;AAAA,UACpD,QAAQ;AAAA,QACV,CAAC;AAAA,MACH;AAAA,IACF;AAAA,IACA,IAAI,WAAW;AACb,aAAO,MAAM;AAAA,IACf;AAAA,EACF,CAAC;AACH;AACA,SAAS,SAAS,OAAO;AACvB,MAAIC,WAAU,GACZ,MACA,KACA,GACA,SACA;AACF,QAAM,CAAC,YAAY,WAAW,IAAI,aAAa,KAAK,GAClDC,mBAAkB,mBAAmB,GACrC,QAAQ;AAAA,IACN,WAAW,MAAM;AACf,UAAI,EAAED,aAAY,EAAG,aAAY,IAAI;AAAA,IACvC;AAAA,IACA,WAAW,MAAM;AACf,UAAI,EAAEA,aAAY,EAAG,aAAY,KAAK;AAAA,IACxC;AAAA,IACA;AAAA,IACA,SAAS,CAAC;AAAA,IACV,UAAU;AAAA,EACZ,GACA,QAAQ,SAAS;AACnB,MAAI,aAAa,WAAW,aAAa,MAAM;AAC7C,UAAM,MAAM,aAAa,aAAa;AACtC,QAAI,MAAM,aAAa,KAAK,GAAG;AAC/B,QAAI,KAAK;AACP,UAAI,OAAO,QAAQ,YAAY,IAAI,MAAM,EAAG,KAAI;AAAA,UAAS,cAAa,OAAO,GAAG;AAAA,IAClF;AACA,QAAI,KAAK,MAAM,OAAO;AACpB,YAAM,CAAC,GAAG,GAAG,IAAI,aAAa,QAAW;AAAA,QACvC,QAAQ;AAAA,MACV,CAAC;AACD,gBAAU;AACV,QAAE,KAAK,MAAM;AACX,YAAI,aAAa,KAAM,QAAO,IAAI;AAClC,qBAAa,OAAO,GAAG;AACvB,0BAAkB,GAAG;AACrB,YAAI;AACJ,0BAAkB;AAAA,MACpB,GAAG,SAAO;AACR,gBAAQ;AACR,YAAI;AAAA,MACN,CAAC;AAAA,IACH;AAAA,EACF;AACA,QAAM,cAAc,WAAW,mBAAmB;AAClD,MAAI,YAAa,QAAO,YAAY,SAAS,MAAM,UAAU;AAC7D,MAAIN;AACJ,YAAU,MAAMA,YAAWA,SAAQ,CAAC;AACpC,SAAO,gBAAgBO,iBAAgB,UAAU;AAAA,IAC/C,OAAO;AAAA,IACP,IAAI,WAAW;AACb,aAAO,WAAW,MAAM;AACtB,YAAI,MAAO,OAAM;AACjB,cAAM,aAAa;AACnB,YAAI,SAAS;AACX,kBAAQ;AACR,iBAAO,UAAU;AAAA,QACnB;AACA,YAAI,OAAO,MAAM,MAAO,mBAAkB;AAC1C,cAAM,WAAW,WAAW,MAAM,MAAM,QAAQ;AAChD,eAAO,WAAW,UAAQ;AACxB,gBAAMJ,cAAa,MAAM,WAAW,GAClC;AAAA,YACE,cAAc;AAAA,YACd,eAAe;AAAA,UACjB,IAAI,OAAO,KAAK,IAAI,CAAC;AACvB,eAAK,CAACA,eAAc,KAAK,MAAM,UAAU,aAAa;AACpD,kBAAM,WAAW;AACjB,YAAAH,YAAWA,SAAQ;AACnB,YAAAA,WAAU,MAAM,IAAI;AACpB,0BAAc,MAAM,OAAO;AAC3B,mBAAO,SAAS;AAAA,UAClB;AACA,cAAI,CAAC,aAAc;AACnB,cAAIA,SAAS,QAAO;AACpB,iBAAO,WAAW,cAAY;AAC5B,YAAAA,WAAU;AACV,gBAAI,KAAK;AACP,gCAAkB;AAAA,gBAChB,IAAI,IAAI,KAAK;AAAA,gBACb,OAAO;AAAA,cACT,CAAC;AACD,oBAAM;AAAA,YACR;AACA,mBAAO,MAAM;AAAA,UACf,GAAG,KAAK;AAAA,QACV,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACH;AAEA,IAAM,MAAM;AAAA,EACV,OAAO;AAAA,EACP;AAAA,EACA;AACF;AACA,IAAI,YAAY;AACd,MAAI,CAAC,WAAW,QAAS,YAAW,UAAU;AAAA,MAAU,SAAQ,KAAK,uFAAuF;AAC9J;", "names": ["taskQueue", "value", "error", "children", "untrack", "e", "dispose", "i", "sources", "inFallback", "res", "registry", "counter", "SuspenseContext"]}