{"version": 3, "sources": ["../../pinia-plugin-persistedstate/dist/index.js"], "sourcesContent": ["// src/normalize.ts\nfunction isObject(v) {\n  return typeof v === \"object\" && v !== null;\n}\nfunction normalizeOptions(options, factoryOptions) {\n  options = isObject(options) ? options : /* @__PURE__ */ Object.create(null);\n  return new Proxy(options, {\n    get(target, key, receiver) {\n      if (key === \"key\")\n        return Reflect.get(target, key, receiver);\n      return Reflect.get(target, key, receiver) || Reflect.get(factoryOptions, key, receiver);\n    }\n  });\n}\n\n// src/pick.ts\nfunction get(state, path) {\n  return path.reduce((obj, p) => {\n    return obj == null ? void 0 : obj[p];\n  }, state);\n}\nfunction set(state, path, val) {\n  return path.slice(0, -1).reduce((obj, p) => {\n    if (/^(__proto__)$/.test(p))\n      return {};\n    else return obj[p] = obj[p] || {};\n  }, state)[path[path.length - 1]] = val, state;\n}\nfunction pick(baseState, paths) {\n  return paths.reduce((substate, path) => {\n    const pathArray = path.split(\".\");\n    return set(substate, pathArray, get(baseState, pathArray));\n  }, {});\n}\n\n// src/plugin.ts\nfunction parsePersistence(factoryOptions, store) {\n  return (o) => {\n    var _a;\n    try {\n      const {\n        storage = localStorage,\n        beforeRestore = void 0,\n        afterRestore = void 0,\n        serializer = {\n          serialize: JSON.stringify,\n          deserialize: JSON.parse\n        },\n        key = store.$id,\n        paths = null,\n        debug = false\n      } = o;\n      return {\n        storage,\n        beforeRestore,\n        afterRestore,\n        serializer,\n        key: ((_a = factoryOptions.key) != null ? _a : (k) => k)(typeof key == \"string\" ? key : key(store.$id)),\n        paths,\n        debug\n      };\n    } catch (e) {\n      if (o.debug)\n        console.error(\"[pinia-plugin-persistedstate]\", e);\n      return null;\n    }\n  };\n}\nfunction hydrateStore(store, { storage, serializer, key, debug }) {\n  try {\n    const fromStorage = storage == null ? void 0 : storage.getItem(key);\n    if (fromStorage)\n      store.$patch(serializer == null ? void 0 : serializer.deserialize(fromStorage));\n  } catch (e) {\n    if (debug)\n      console.error(\"[pinia-plugin-persistedstate]\", e);\n  }\n}\nfunction persistState(state, { storage, serializer, key, paths, debug }) {\n  try {\n    const toStore = Array.isArray(paths) ? pick(state, paths) : state;\n    storage.setItem(key, serializer.serialize(toStore));\n  } catch (e) {\n    if (debug)\n      console.error(\"[pinia-plugin-persistedstate]\", e);\n  }\n}\nfunction createPersistedState(factoryOptions = {}) {\n  return (context) => {\n    const { auto = false } = factoryOptions;\n    const {\n      options: { persist = auto },\n      store,\n      pinia\n    } = context;\n    if (!persist)\n      return;\n    if (!(store.$id in pinia.state.value)) {\n      const original_store = pinia._s.get(store.$id.replace(\"__hot:\", \"\"));\n      if (original_store)\n        Promise.resolve().then(() => original_store.$persist());\n      return;\n    }\n    const persistences = (Array.isArray(persist) ? persist.map((p) => normalizeOptions(p, factoryOptions)) : [normalizeOptions(persist, factoryOptions)]).map(parsePersistence(factoryOptions, store)).filter(Boolean);\n    store.$persist = () => {\n      persistences.forEach((persistence) => {\n        persistState(store.$state, persistence);\n      });\n    };\n    store.$hydrate = ({ runHooks = true } = {}) => {\n      persistences.forEach((persistence) => {\n        const { beforeRestore, afterRestore } = persistence;\n        if (runHooks)\n          beforeRestore == null ? void 0 : beforeRestore(context);\n        hydrateStore(store, persistence);\n        if (runHooks)\n          afterRestore == null ? void 0 : afterRestore(context);\n      });\n    };\n    persistences.forEach((persistence) => {\n      const { beforeRestore, afterRestore } = persistence;\n      beforeRestore == null ? void 0 : beforeRestore(context);\n      hydrateStore(store, persistence);\n      afterRestore == null ? void 0 : afterRestore(context);\n      store.$subscribe(\n        (_mutation, state) => {\n          persistState(state, persistence);\n        },\n        {\n          detached: true\n        }\n      );\n    });\n  };\n}\n\n// src/index.ts\nvar src_default = createPersistedState();\nexport {\n  createPersistedState,\n  src_default as default\n};\n"], "mappings": ";;;AACA,SAAS,SAAS,GAAG;AACnB,SAAO,OAAO,MAAM,YAAY,MAAM;AACxC;AACA,SAAS,iBAAiB,SAAS,gBAAgB;AACjD,YAAU,SAAS,OAAO,IAAI,UAA0B,uBAAO,OAAO,IAAI;AAC1E,SAAO,IAAI,MAAM,SAAS;AAAA,IACxB,IAAI,QAAQ,KAAK,UAAU;AACzB,UAAI,QAAQ;AACV,eAAO,QAAQ,IAAI,QAAQ,KAAK,QAAQ;AAC1C,aAAO,QAAQ,IAAI,QAAQ,KAAK,QAAQ,KAAK,QAAQ,IAAI,gBAAgB,KAAK,QAAQ;AAAA,IACxF;AAAA,EACF,CAAC;AACH;AAGA,SAAS,IAAI,OAAO,MAAM;AACxB,SAAO,KAAK,OAAO,CAAC,KAAK,MAAM;AAC7B,WAAO,OAAO,OAAO,SAAS,IAAI,CAAC;AAAA,EACrC,GAAG,KAAK;AACV;AACA,SAAS,IAAI,OAAO,MAAM,KAAK;AAC7B,SAAO,KAAK,MAAM,GAAG,EAAE,EAAE,OAAO,CAAC,KAAK,MAAM;AAC1C,QAAI,gBAAgB,KAAK,CAAC;AACxB,aAAO,CAAC;AAAA,QACL,QAAO,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC;AAAA,EAClC,GAAG,KAAK,EAAE,KAAK,KAAK,SAAS,CAAC,CAAC,IAAI,KAAK;AAC1C;AACA,SAAS,KAAK,WAAW,OAAO;AAC9B,SAAO,MAAM,OAAO,CAAC,UAAU,SAAS;AACtC,UAAM,YAAY,KAAK,MAAM,GAAG;AAChC,WAAO,IAAI,UAAU,WAAW,IAAI,WAAW,SAAS,CAAC;AAAA,EAC3D,GAAG,CAAC,CAAC;AACP;AAGA,SAAS,iBAAiB,gBAAgB,OAAO;AAC/C,SAAO,CAAC,MAAM;AACZ,QAAI;AACJ,QAAI;AACF,YAAM;AAAA,QACJ,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,eAAe;AAAA,QACf,aAAa;AAAA,UACX,WAAW,KAAK;AAAA,UAChB,aAAa,KAAK;AAAA,QACpB;AAAA,QACA,MAAM,MAAM;AAAA,QACZ,QAAQ;AAAA,QACR,QAAQ;AAAA,MACV,IAAI;AACJ,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,OAAO,KAAK,eAAe,QAAQ,OAAO,KAAK,CAAC,MAAM,GAAG,OAAO,OAAO,WAAW,MAAM,IAAI,MAAM,GAAG,CAAC;AAAA,QACtG;AAAA,QACA;AAAA,MACF;AAAA,IACF,SAAS,GAAG;AACV,UAAI,EAAE;AACJ,gBAAQ,MAAM,iCAAiC,CAAC;AAClD,aAAO;AAAA,IACT;AAAA,EACF;AACF;AACA,SAAS,aAAa,OAAO,EAAE,SAAS,YAAY,KAAK,MAAM,GAAG;AAChE,MAAI;AACF,UAAM,cAAc,WAAW,OAAO,SAAS,QAAQ,QAAQ,GAAG;AAClE,QAAI;AACF,YAAM,OAAO,cAAc,OAAO,SAAS,WAAW,YAAY,WAAW,CAAC;AAAA,EAClF,SAAS,GAAG;AACV,QAAI;AACF,cAAQ,MAAM,iCAAiC,CAAC;AAAA,EACpD;AACF;AACA,SAAS,aAAa,OAAO,EAAE,SAAS,YAAY,KAAK,OAAO,MAAM,GAAG;AACvE,MAAI;AACF,UAAM,UAAU,MAAM,QAAQ,KAAK,IAAI,KAAK,OAAO,KAAK,IAAI;AAC5D,YAAQ,QAAQ,KAAK,WAAW,UAAU,OAAO,CAAC;AAAA,EACpD,SAAS,GAAG;AACV,QAAI;AACF,cAAQ,MAAM,iCAAiC,CAAC;AAAA,EACpD;AACF;AACA,SAAS,qBAAqB,iBAAiB,CAAC,GAAG;AACjD,SAAO,CAAC,YAAY;AAClB,UAAM,EAAE,OAAO,MAAM,IAAI;AACzB,UAAM;AAAA,MACJ,SAAS,EAAE,UAAU,KAAK;AAAA,MAC1B;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,CAAC;AACH;AACF,QAAI,EAAE,MAAM,OAAO,MAAM,MAAM,QAAQ;AACrC,YAAM,iBAAiB,MAAM,GAAG,IAAI,MAAM,IAAI,QAAQ,UAAU,EAAE,CAAC;AACnE,UAAI;AACF,gBAAQ,QAAQ,EAAE,KAAK,MAAM,eAAe,SAAS,CAAC;AACxD;AAAA,IACF;AACA,UAAM,gBAAgB,MAAM,QAAQ,OAAO,IAAI,QAAQ,IAAI,CAAC,MAAM,iBAAiB,GAAG,cAAc,CAAC,IAAI,CAAC,iBAAiB,SAAS,cAAc,CAAC,GAAG,IAAI,iBAAiB,gBAAgB,KAAK,CAAC,EAAE,OAAO,OAAO;AACjN,UAAM,WAAW,MAAM;AACrB,mBAAa,QAAQ,CAAC,gBAAgB;AACpC,qBAAa,MAAM,QAAQ,WAAW;AAAA,MACxC,CAAC;AAAA,IACH;AACA,UAAM,WAAW,CAAC,EAAE,WAAW,KAAK,IAAI,CAAC,MAAM;AAC7C,mBAAa,QAAQ,CAAC,gBAAgB;AACpC,cAAM,EAAE,eAAe,aAAa,IAAI;AACxC,YAAI;AACF,2BAAiB,OAAO,SAAS,cAAc,OAAO;AACxD,qBAAa,OAAO,WAAW;AAC/B,YAAI;AACF,0BAAgB,OAAO,SAAS,aAAa,OAAO;AAAA,MACxD,CAAC;AAAA,IACH;AACA,iBAAa,QAAQ,CAAC,gBAAgB;AACpC,YAAM,EAAE,eAAe,aAAa,IAAI;AACxC,uBAAiB,OAAO,SAAS,cAAc,OAAO;AACtD,mBAAa,OAAO,WAAW;AAC/B,sBAAgB,OAAO,SAAS,aAAa,OAAO;AACpD,YAAM;AAAA,QACJ,CAAC,WAAW,UAAU;AACpB,uBAAa,OAAO,WAAW;AAAA,QACjC;AAAA,QACA;AAAA,UACE,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAGA,IAAI,cAAc,qBAAqB;", "names": []}