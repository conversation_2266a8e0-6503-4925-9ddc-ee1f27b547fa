import { createSignal, createEffect, onMount, onCleanup, Show, For } from 'solid-js'
import { createStore } from 'solid-js/store'
import { css } from '../../styled-system/css'
import { formatPrice } from '../../utils/formatters'

interface OrderBookLevel {
  price: number
  volume: number
  count: number
}

interface OrderBookData {
  symbol: string
  timestamp: number
  bids: OrderBookLevel[]
  asks: OrderBookLevel[]
  lastPrice?: number
  lastVolume?: number
}

interface Props {
  symbol: string
  levels?: number
  precision?: number
  onPriceSelect?: (price: number) => void
  autoRefresh?: boolean
  refreshInterval?: number
  size?: 'compact' | 'normal' | 'expanded'
}

export function OrderBook(props: Props) {
  const [orderBook, setOrderBook] = createStore<OrderBookData>({
    symbol: props.symbol,
    timestamp: 0,
    bids: [],
    asks: []
  })
  
  const [loading, setLoading] = createSignal(true)
  const [connected, setConnected] = createSignal(false)
  const [lastUpdateTime, setLastUpdateTime] = createSignal<string>('')
  
  let refreshTimer: number | undefined

  // Configuration
  const levels = () => props.levels || 10
  const precision = () => props.precision || 2
  const refreshInterval = () => props.refreshInterval || 100 // 100ms

  // Mock data generator
  const generateMockOrderBook = (): OrderBookData => {
    const basePrice = 3850 + (Math.random() - 0.5) * 20
    const bids: OrderBookLevel[] = []
    const asks: OrderBookLevel[] = []
    
    // Generate bids (below current price)
    for (let i = 0; i < levels(); i++) {
      const price = basePrice - (i + 1) * 0.2
      bids.push({
        price: Number(price.toFixed(precision())),
        volume: Math.floor(Math.random() * 1000) + 100,
        count: Math.floor(Math.random() * 50) + 1
      })
    }
    
    // Generate asks (above current price)
    for (let i = 0; i < levels(); i++) {
      const price = basePrice + (i + 1) * 0.2
      asks.push({
        price: Number(price.toFixed(precision())),
        volume: Math.floor(Math.random() * 1000) + 100,
        count: Math.floor(Math.random() * 50) + 1
      })
    }
    
    // Sort bids descending, asks ascending
    bids.sort((a, b) => b.price - a.price)
    asks.sort((a, b) => a.price - b.price)
    
    return {
      symbol: props.symbol,
      timestamp: Date.now(),
      bids,
      asks,
      lastPrice: basePrice,
      lastVolume: Math.floor(Math.random() * 500) + 50
    }
  }

  // Update order book data
  const updateOrderBook = async () => {
    try {
      // Simulate API call or WebSocket update
      const newData = generateMockOrderBook()
      setOrderBook(newData)
      setLastUpdateTime(new Date().toLocaleTimeString())
      setConnected(true)
      setLoading(false)
    } catch (error) {
      console.error('Failed to update order book:', error)
      setConnected(false)
    }
  }

  // Calculate cumulative volumes
  const calculateCumulativeVolumes = () => {
    const bidsCumulative = orderBook.bids.map((level, index) => {
      const cumVolume = orderBook.bids
        .slice(0, index + 1)
        .reduce((sum, bid) => sum + bid.volume, 0)
      return { ...level, cumVolume }
    })

    const asksCumulative = orderBook.asks.map((level, index) => {
      const cumVolume = orderBook.asks
        .slice(0, index + 1)
        .reduce((sum, ask) => sum + ask.volume, 0)
      return { ...level, cumVolume }
    })

    const maxCumVolume = Math.max(
      Math.max(...bidsCumulative.map(b => b.cumVolume), 0),
      Math.max(...asksCumulative.map(a => a.cumVolume), 0)
    )

    return {
      bids: bidsCumulative,
      asks: asksCumulative,
      maxCumVolume
    }
  }

  const { bids: bidsWithCum, asks: asksWithCum, maxCumVolume } = calculateCumulativeVolumes()

  // Price click handler
  const handlePriceClick = (price: number) => {
    props.onPriceSelect?.(price)
  }

  // Get volume bar width percentage
  const getVolumeBarWidth = (cumVolume: number): number => {
    return maxCumVolume > 0 ? (cumVolume / maxCumVolume) * 100 : 0
  }

  // Component size configuration
  const getSizeConfig = () => {
    switch (props.size) {
      case 'compact':
        return {
          fontSize: '11px',
          padding: '2px 4px',
          headerPadding: '6px 8px'
        }
      case 'expanded':
        return {
          fontSize: '14px',
          padding: '4px 8px',
          headerPadding: '12px 16px'
        }
      default:
        return {
          fontSize: '12px',
          padding: '3px 6px',
          headerPadding: '8px 12px'
        }
    }
  }

  const sizeConfig = getSizeConfig()

  // Setup auto-refresh
  createEffect(() => {
    if (props.autoRefresh !== false) {
      refreshTimer = setInterval(updateOrderBook, refreshInterval()) as unknown as number
    }
    
    return () => {
      if (refreshTimer) {
        clearInterval(refreshTimer)
      }
    }
  })

  // Initial load
  onMount(() => {
    updateOrderBook()
  })

  onCleanup(() => {
    if (refreshTimer) {
      clearInterval(refreshTimer)
    }
  })

  return (
    <div class={css({
      bg: 'white',
      borderRadius: '6px',
      border: '1px solid #ebeef5',
      overflow: 'hidden',
      fontFamily: 'monospace'
    })}>
      {/* Header */}
      <div class={css({
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: sizeConfig.headerPadding,
        bg: '#f5f7fa',
        borderBottom: '1px solid #ebeef5'
      })}>
        <div class={css({
          fontSize: sizeConfig.fontSize,
          fontWeight: 'bold',
          color: '#303133'
        })}>
          {props.symbol} 订单簿
        </div>
        
        <div class={css({
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
          fontSize: props.size === 'compact' ? '10px' : '11px',
          color: '#909399'
        })}>
          <div class={css({
            width: '6px',
            height: '6px',
            borderRadius: '50%',
            bg: connected() ? '#67c23a' : '#f56c6c'
          })} />
          {lastUpdateTime()}
        </div>
      </div>

      <Show when={!loading()} fallback={
        <div class={css({
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '200px',
          color: '#909399'
        })}>
          <div class={css({
            width: '20px',
            height: '20px',
            border: '2px solid #f3f3f3',
            borderTop: '2px solid #409eff',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite'
          })} />
        </div>
      }>
        <div class={css({ fontSize: sizeConfig.fontSize })}>
          {/* Column Headers */}
          <div class={css({
            display: 'grid',
            gridTemplateColumns: '1fr 1fr 1fr',
            padding: sizeConfig.padding,
            bg: '#fafafa',
            fontSize: props.size === 'compact' ? '10px' : '11px',
            color: '#909399',
            fontWeight: 'bold',
            borderBottom: '1px solid #f0f0f0'
          })}>
            <div class={css({ textAlign: 'left' })}>价格</div>
            <div class={css({ textAlign: 'right' })}>数量</div>
            <div class={css({ textAlign: 'right' })}>累计</div>
          </div>

          {/* Asks (卖盘) */}
          <div class={css({ maxHeight: '150px', overflowY: 'auto' })}>
            <For each={[...asksWithCum].reverse()}>
              {(ask) => (
                <div
                  onClick={() => handlePriceClick(ask.price)}
                  class={css({
                    position: 'relative',
                    display: 'grid',
                    gridTemplateColumns: '1fr 1fr 1fr',
                    padding: sizeConfig.padding,
                    cursor: 'pointer',
                    borderBottom: '1px solid #f8f8f8',
                    '&:hover': {
                      bg: 'rgba(67, 194, 58, 0.1)'
                    }
                  })}
                >
                  {/* Volume Bar Background */}
                  <div
                    class={css({
                      position: 'absolute',
                      top: 0,
                      right: 0,
                      bottom: 0,
                      bg: 'rgba(67, 194, 58, 0.1)',
                      borderRadius: '2px',
                      transition: 'width 0.3s ease'
                    })}
                    style={{ width: `${getVolumeBarWidth(ask.cumVolume)}%` }}
                  />
                  
                  <div class={css({
                    color: '#67c23a',
                    fontWeight: 'bold',
                    position: 'relative',
                    zIndex: 1
                  })}>
                    {formatPrice(ask.price)}
                  </div>
                  <div class={css({
                    textAlign: 'right',
                    color: '#303133',
                    position: 'relative',
                    zIndex: 1
                  })}>
                    {ask.volume}
                  </div>
                  <div class={css({
                    textAlign: 'right',
                    color: '#909399',
                    position: 'relative',
                    zIndex: 1
                  })}>
                    {ask.cumVolume}
                  </div>
                </div>
              )}
            </For>
          </div>

          {/* Current Price */}
          <Show when={orderBook.lastPrice}>
            <div class={css({
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              padding: props.size === 'compact' ? '4px' : '6px',
              bg: '#fff7e6',
              border: '1px solid #ffd666',
              fontSize: props.size === 'compact' ? '11px' : '13px',
              fontWeight: 'bold',
              color: '#d48806'
            })}>
              最新价: {formatPrice(orderBook.lastPrice!)}
              <Show when={orderBook.lastVolume}>
                <span class={css({
                  marginLeft: '8px',
                  fontSize: props.size === 'compact' ? '10px' : '12px',
                  color: '#909399'
                })}>
                  {orderBook.lastVolume}
                </span>
              </Show>
            </div>
          </Show>

          {/* Bids (买盘) */}
          <div class={css({ maxHeight: '150px', overflowY: 'auto' })}>
            <For each={bidsWithCum}>
              {(bid) => (
                <div
                  onClick={() => handlePriceClick(bid.price)}
                  class={css({
                    position: 'relative',
                    display: 'grid',
                    gridTemplateColumns: '1fr 1fr 1fr',
                    padding: sizeConfig.padding,
                    cursor: 'pointer',
                    borderBottom: '1px solid #f8f8f8',
                    '&:hover': {
                      bg: 'rgba(245, 108, 108, 0.1)'
                    }
                  })}
                >
                  {/* Volume Bar Background */}
                  <div
                    class={css({
                      position: 'absolute',
                      top: 0,
                      right: 0,
                      bottom: 0,
                      bg: 'rgba(245, 108, 108, 0.1)',
                      borderRadius: '2px',
                      transition: 'width 0.3s ease'
                    })}
                    style={{ width: `${getVolumeBarWidth(bid.cumVolume)}%` }}
                  />
                  
                  <div class={css({
                    color: '#f56c6c',
                    fontWeight: 'bold',
                    position: 'relative',
                    zIndex: 1
                  })}>
                    {formatPrice(bid.price)}
                  </div>
                  <div class={css({
                    textAlign: 'right',
                    color: '#303133',
                    position: 'relative',
                    zIndex: 1
                  })}>
                    {bid.volume}
                  </div>
                  <div class={css({
                    textAlign: 'right',
                    color: '#909399',
                    position: 'relative',
                    zIndex: 1
                  })}>
                    {bid.cumVolume}
                  </div>
                </div>
              )}
            </For>
          </div>

          {/* Footer Stats */}
          <Show when={props.size !== 'compact'}>
            <div class={css({
              display: 'flex',
              justifyContent: 'space-around',
              padding: '6px',
              bg: '#f9f9f9',
              borderTop: '1px solid #f0f0f0',
              fontSize: '10px',
              color: '#909399'
            })}>
              <div>
                买盘总量: {orderBook.bids.reduce((sum, bid) => sum + bid.volume, 0)}
              </div>
              <div>
                卖盘总量: {orderBook.asks.reduce((sum, ask) => sum + ask.volume, 0)}
              </div>
            </div>
          </Show>
        </div>
      </Show>

      <style>
        {`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}
      </style>
    </div>
  )
}

export default OrderBook