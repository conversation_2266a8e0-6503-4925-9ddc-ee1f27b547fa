import { JSX, Show } from 'solid-js'
import { css } from '../../styled-system/css'

interface MetricCardProps {
  title: string
  value: string
  unit?: string
  change?: number
  changePercent?: number
  type?: 'primary' | 'success' | 'danger' | 'info'
  icon?: string
  showChart?: boolean
  clickable?: boolean
  onClick?: () => void
}

export default function MetricCard(props: MetricCardProps) {
  const getTypeColor = () => {
    switch (props.type) {
      case 'primary':
        return {
          bg: '#e6f7ff',
          border: '#91d5ff',
          icon: '#1890ff'
        }
      case 'success':
        return {
          bg: '#f6ffed',
          border: '#b7eb8f',
          icon: '#52c41a'
        }
      case 'danger':
        return {
          bg: '#fff2f0',
          border: '#ffccc7',
          icon: '#ff4d4f'
        }
      case 'info':
        return {
          bg: '#f0f5ff',
          border: '#adc6ff',
          icon: '#722ed1'
        }
      default:
        return {
          bg: '#fafafa',
          border: '#d9d9d9',
          icon: '#666'
        }
    }
  }

  const getChangeColor = () => {
    if (props.change === undefined) return '#666'
    return props.change >= 0 ? '#52c41a' : '#ff4d4f'
  }

  const colors = getTypeColor()

  return (
    <div
      class={css({
        bg: 'white',
        border: `1px solid ${colors.border}`,
        borderRadius: '8px',
        p: '20px',
        cursor: props.clickable ? 'pointer' : 'default',
        transition: 'all 0.3s ease',
        _hover: props.clickable ? {
          transform: 'translateY(-2px)',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
        } : {}
      })}
      onClick={props.onClick}
    >
      {/* 标题和图标 */}
      <div class={css({
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        mb: '12px'
      })}>
        <span class={css({
          fontSize: '14px',
          color: '#666',
          fontWeight: '500'
        })}>
          {props.title}
        </span>
        <Show when={props.icon}>
          <div class={css({
            width: '24px',
            height: '24px',
            bg: colors.bg,
            borderRadius: '4px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '12px'
          })}>
            {props.icon}
          </div>
        </Show>
      </div>

      {/* 主要数值 */}
      <div class={css({
        display: 'flex',
        alignItems: 'baseline',
        mb: '8px'
      })}>
        <Show when={props.unit}>
          <span class={css({
            fontSize: '16px',
            color: '#999',
            mr: '4px'
          })}>
            {props.unit}
          </span>
        </Show>
        <span class={css({
          fontSize: '24px',
          fontWeight: 'bold',
          color: '#333'
        })}>
          {props.value}
        </span>
      </div>

      {/* 变化值和百分比 */}
      <Show when={props.change !== undefined || props.changePercent !== undefined}>
        <div class={css({
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
          fontSize: '12px'
        })}>
          <Show when={props.change !== undefined}>
            <span class={css({
              color: getChangeColor(),
              fontWeight: '500'
            })}>
              {props.change! >= 0 ? '+' : ''}{props.change!.toLocaleString()}
            </span>
          </Show>
          <Show when={props.changePercent !== undefined}>
            <span class={css({
              color: getChangeColor(),
              fontWeight: '500'
            })}>
              ({props.changePercent! >= 0 ? '+' : ''}{props.changePercent!.toFixed(2)}%)
            </span>
          </Show>
        </div>
      </Show>

      {/* 迷你图表区域 */}
      <Show when={props.showChart}>
        <div class={css({
          mt: '12px',
          height: '40px',
          bg: colors.bg,
          borderRadius: '4px',
          position: 'relative',
          overflow: 'hidden'
        })}>
          {/* 简单的趋势线模拟 */}
          <div class={css({
            position: 'absolute',
            bottom: '8px',
            left: '8px',
            right: '8px',
            height: '2px',
            bg: colors.icon,
            borderRadius: '1px'
          })} />
          <div class={css({
            position: 'absolute',
            bottom: '8px',
            right: '20px',
            width: '6px',
            height: '6px',
            bg: colors.icon,
            borderRadius: '50%'
          })} />
        </div>
      </Show>
    </div>
  )
}
