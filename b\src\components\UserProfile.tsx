import { Show } from 'solid-js';
import { userStore } from '../stores';

export default function UserProfile() {
  const user = () => userStore.state.user;
  const isAuthenticated = () => userStore.state.isAuthenticated;

  return (
    <Show when={isAuthenticated() && user()}>
      <div style={{
        background: 'white',
        'border-radius': '12px',
        padding: '24px',
        'box-shadow': '0 4px 12px rgba(0,0,0,0.1)',
        margin: '20px 0'
      }}>
        <h2 style={{
          'font-size': '20px',
          'font-weight': 'bold',
          color: '#111827',
          'margin-bottom': '20px',
          display: 'flex',
          'align-items': 'center',
          gap: '8px'
        }}>
          👤 用户信息
        </h2>

        <div style={{
          display: 'grid',
          'grid-template-columns': 'auto 1fr',
          gap: '16px',
          'align-items': 'center'
        }}>
          {/* 头像 */}
          <div style={{
            gridColumn: '1 / -1',
            display: 'flex',
            'align-items': 'center',
            gap: '16px',
            'margin-bottom': '16px'
          }}>
            <img
              src={user()?.avatar}
              alt="用户头像"
              style={{
                width: '64px',
                height: '64px',
                'border-radius': '50%',
                border: '3px solid #e5e7eb'
              }}
            />
            <div>
              <h3 style={{
                'font-size': '18px',
                'font-weight': '600',
                color: '#111827',
                margin: '0 0 4px 0'
              }}>
                {user()?.nickname}
              </h3>
              <p style={{
                'font-size': '14px',
                color: '#6b7280',
                margin: '0'
              }}>
                @{user()?.username}
              </p>
            </div>
          </div>

          {/* 基本信息 */}
          <span style={{ 'font-weight': '500', color: '#374151' }}>用户ID:</span>
          <span style={{ color: '#6b7280', fontFamily: 'monospace' }}>{user()?.id}</span>

          <span style={{ 'font-weight': '500', color: '#374151' }}>邮箱:</span>
          <span style={{ color: '#6b7280' }}>{user()?.email}</span>

          <span style={{ 'font-weight': '500', color: '#374151' }}>手机:</span>
          <span style={{ color: '#6b7280' }}>{user()?.phone}</span>

          <span style={{ 'font-weight': '500', color: '#374151' }}>角色:</span>
          <span style={{
            color: 'white',
            'background-color': user()?.role === 'admin' ? '#ef4444' : user()?.role === 'trader' ? '#3b82f6' : '#10b981',
            padding: '2px 8px',
            'border-radius': '12px',
            'font-size': '12px',
            'font-weight': '500'
          }}>
            {user()?.role === 'admin' ? '管理员' : user()?.role === 'trader' ? '交易员' : '普通用户'}
          </span>

          <span style={{ 'font-weight': '500', color: '#374151' }}>状态:</span>
          <span style={{
            color: user()?.status === 'active' ? '#10b981' : '#ef4444',
            'font-weight': '500'
          }}>
            {user()?.status === 'active' ? '✅ 活跃' : '❌ 禁用'}
          </span>

          <span style={{ 'font-weight': '500', color: '#374151' }}>注册时间:</span>
          <span style={{ color: '#6b7280' }}>
            {new Date(user()?.createdAt || '').toLocaleDateString('zh-CN')}
          </span>

          <span style={{ 'font-weight': '500', color: '#374151' }}>最后登录:</span>
          <span style={{ color: '#6b7280' }}>
            {new Date(user()?.lastLoginAt || '').toLocaleString('zh-CN')}
          </span>
        </div>

        {/* 偏好设置 */}
        <div style={{
          'margin-top': '24px',
          'padding-top': '20px',
          'border-top': '1px solid #e5e7eb'
        }}>
          <h4 style={{
            'font-size': '16px',
            'font-weight': '600',
            color: '#111827',
            'margin-bottom': '12px'
          }}>
            偏好设置
          </h4>
          <div style={{
            display: 'grid',
            'grid-template-columns': 'auto 1fr',
            gap: '12px',
            'font-size': '14px'
          }}>
            <span style={{ 'font-weight': '500', color: '#374151' }}>主题:</span>
            <span style={{ color: '#6b7280' }}>
              {user()?.preferences?.theme === 'light' ? '🌞 浅色' : '🌙 深色'}
            </span>

            <span style={{ 'font-weight': '500', color: '#374151' }}>语言:</span>
            <span style={{ color: '#6b7280' }}>
              {user()?.preferences?.language === 'zh-CN' ? '🇨🇳 中文' : '🇺🇸 English'}
            </span>

            <span style={{ 'font-weight': '500', color: '#374151' }}>时区:</span>
            <span style={{ color: '#6b7280' }}>{user()?.preferences?.timezone}</span>
          </div>
        </div>

        {/* 个人资料 */}
        <div style={{
          'margin-top': '20px',
          'padding-top': '20px',
          'border-top': '1px solid #e5e7eb'
        }}>
          <h4 style={{
            'font-size': '16px',
            'font-weight': '600',
            color: '#111827',
            'margin-bottom': '12px'
          }}>
            个人资料
          </h4>
          <div style={{
            display: 'grid',
            'grid-template-columns': 'auto 1fr',
            gap: '12px',
            'font-size': '14px'
          }}>
            <span style={{ 'font-weight': '500', color: '#374151' }}>真实姓名:</span>
            <span style={{ color: '#6b7280' }}>{user()?.profile?.realName}</span>

            <span style={{ 'font-weight': '500', color: '#374151' }}>性别:</span>
            <span style={{ color: '#6b7280' }}>
              {user()?.profile?.gender === 'male' ? '👨 男' : '👩 女'}
            </span>

            <span style={{ 'font-weight': '500', color: '#374151' }}>投资经验:</span>
            <span style={{ color: '#6b7280' }}>
              {user()?.profile?.investmentExperience === 'beginner' ? '🔰 新手' :
               user()?.profile?.investmentExperience === 'intermediate' ? '📈 中级' : '🎯 专家'}
            </span>

            <span style={{ 'font-weight': '500', color: '#374151' }}>风险承受:</span>
            <span style={{ color: '#6b7280' }}>
              {user()?.profile?.riskTolerance === 'conservative' ? '🛡️ 保守' :
               user()?.profile?.riskTolerance === 'moderate' ? '⚖️ 稳健' : '🚀 激进'}
            </span>
          </div>
        </div>

        {/* 操作按钮 */}
        <div style={{
          'margin-top': '24px',
          display: 'flex',
          gap: '12px',
          'justify-content': 'flex-end'
        }}>
          <button
            style={{
              padding: '8px 16px',
              'background-color': '#f3f4f6',
              color: '#374151',
              border: 'none',
              'border-radius': '6px',
              cursor: 'pointer',
              'font-size': '14px',
              'font-weight': '500'
            }}
            onClick={() => {
              // 这里可以添加编辑个人资料的逻辑
              alert('编辑个人资料功能开发中...');
            }}
          >
            ✏️ 编辑资料
          </button>
          
          <button
            style={{
              padding: '8px 16px',
              'background-color': '#ef4444',
              color: 'white',
              border: 'none',
              'border-radius': '6px',
              cursor: 'pointer',
              'font-size': '14px',
              'font-weight': '500'
            }}
            onClick={async () => {
              if (confirm('确定要退出登录吗？')) {
                await userStore.logout();
                window.location.href = '/login';
              }
            }}
          >
            🚪 退出登录
          </button>
        </div>
      </div>
    </Show>
  );
}
