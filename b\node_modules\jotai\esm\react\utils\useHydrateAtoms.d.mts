import { useStore } from 'jotai/react';
import type { WritableAtom } from 'jotai/vanilla';
type Options = Parameters<typeof useStore>[0] & {
    dangerouslyForceHydrate?: boolean;
};
type AnyWritableAtom = WritableAtom<unknown, never[], unknown>;
type SpreadArgs<Args extends readonly unknown[]> = Args extends readonly [
    infer First,
    ...infer Rest
] ? readonly [First, ...Rest] : <PERSON>rgs extends readonly [infer Single] ? readonly [Single] : readonly [];
type InferAtomTuples<T> = {
    [K in keyof T]: T[K] extends readonly [infer A, ...infer _Rest] ? A extends WritableAtom<unknown, infer Args, infer _Result> ? Args extends readonly unknown[] ? readonly [A, ...SpreadArgs<Args>] : readonly [A] : T[K] : never;
};
export type INTERNAL_InferAtomTuples<T> = InferAtomTuples<T>;
export declare function useHydrateAtoms<T extends (readonly [AnyWritableAtom, ...any[]])[]>(values: InferAtomTuples<T>, options?: Options): void;
export declare function useHydrateAtoms<T extends Map<AnyWritableAtom, unknown>>(values: T, options?: Options): void;
export declare function useHydrateAtoms<T extends Iterable<readonly [AnyWritableAtom, ...any[]]>>(values: InferAtomTuples<T>, options?: Options): void;
export {};
