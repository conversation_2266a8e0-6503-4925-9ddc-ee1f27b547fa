import { createSignal, Show, onMount } from 'solid-js'
import { createStore } from 'solid-js/store'
import { css } from '../../styled-system/css'
import { formatPrice, formatCurrency } from '../../utils/formatters'
import { useAtom } from 'jotai'
import { tradingAtom, marketDataAtom } from '../../stores/atoms'

interface QuickOrderData {
  symbol: string
  side: 'buy' | 'sell'
  quantity: number
  orderType: 'market' | 'limit'
  price?: number
}

interface Props {
  symbol: string
  currentPrice: number
  onSubmit?: (orderData: QuickOrderData) => void
  size?: 'small' | 'medium' | 'large'
}

export function QuickOrderForm(props: Props) {
  const [trading] = useAtom(tradingAtom)
  const [marketData] = useAtom(marketDataAtom)
  
  const [form, setForm] = createStore<QuickOrderData>({
    symbol: props.symbol,
    side: 'buy',
    quantity: 100,
    orderType: 'market',
    price: props.currentPrice
  })
  
  const [submitting, setSubmitting] = createSignal(false)
  const [errors, setErrors] = createStore<Record<string, string>>({})

  // Update form when props change
  onMount(() => {
    setForm('symbol', props.symbol)
    setForm('price', props.currentPrice)
  })

  // Quick quantity presets
  const quantityPresets = [100, 200, 500, 1000]
  
  // Price adjustment buttons
  const priceAdjustments = [
    { label: '-1%', value: -0.01 },
    { label: '-0.5%', value: -0.005 },
    { label: '现价', value: 0 },
    { label: '+0.5%', value: 0.005 },
    { label: '+1%', value: 0.01 }
  ]

  const availableCash = () => trading.account?.availableCash || 0
  
  const estimatedAmount = () => {
    const price = form.orderType === 'market' ? props.currentPrice : (form.price || 0)
    return price * form.quantity
  }

  const canSubmit = () => {
    if (submitting()) return false
    if (!form.quantity || form.quantity <= 0) return false
    
    if (form.orderType === 'limit' && (!form.price || form.price <= 0)) {
      return false
    }
    
    if (form.side === 'buy') {
      return estimatedAmount() <= availableCash()
    }
    
    return true
  }

  const handleSubmit = async () => {
    if (!canSubmit()) return

    // Validate form
    const newErrors: Record<string, string> = {}
    if (!form.quantity) newErrors.quantity = '请输入数量'
    if (form.orderType === 'limit' && !form.price) newErrors.price = '请输入价格'
    
    setErrors(newErrors)
    if (Object.keys(newErrors).length > 0) return

    setSubmitting(true)
    
    try {
      await new Promise(resolve => setTimeout(resolve, 500))
      
      const orderData: QuickOrderData = {
        symbol: form.symbol,
        side: form.side,
        quantity: form.quantity,
        orderType: form.orderType,
        price: form.orderType === 'limit' ? form.price : undefined
      }
      
      props.onSubmit?.(orderData)
      
      // Reset quantity after successful submission
      setForm('quantity', 100)
      
    } catch (error) {
      console.error('Order submission failed:', error)
    } finally {
      setSubmitting(false)
    }
  }

  const adjustPrice = (adjustment: number) => {
    const basePrice = props.currentPrice
    const newPrice = basePrice * (1 + adjustment)
    setForm('price', Math.round(newPrice * 100) / 100)
  }

  const setQuantity = (quantity: number) => {
    setForm('quantity', quantity)
  }

  // Component size classes
  const getSizeClass = () => {
    switch (props.size) {
      case 'small':
        return css({
          padding: '12px',
          fontSize: '12px'
        })
      case 'large':
        return css({
          padding: '24px',
          fontSize: '16px'
        })
      default:
        return css({
          padding: '16px',
          fontSize: '14px'
        })
    }
  }

  return (
    <div class={css({
      bg: 'white',
      borderRadius: '8px',
      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
      border: '1px solid #ebeef5'
    })}>
      <div class={getSizeClass()}>
        {/* Header */}
        <div class={css({
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '12px',
          paddingBottom: '8px',
          borderBottom: '1px solid #f0f0f0'
        })}>
          <div class={css({
            fontSize: props.size === 'small' ? '14px' : '16px',
            fontWeight: 'bold',
            color: '#303133'
          })}>
            {props.symbol}
          </div>
          <div class={css({
            fontSize: props.size === 'small' ? '12px' : '14px',
            color: '#909399'
          })}>
            {formatPrice(props.currentPrice)}
          </div>
        </div>

        {/* Buy/Sell Toggle */}
        <div class={css({
          display: 'flex',
          marginBottom: '12px',
          border: '1px solid #dcdfe6',
          borderRadius: '4px',
          overflow: 'hidden'
        })}>
          <button
            onClick={() => setForm('side', 'buy')}
            class={css({
              flex: 1,
              padding: '8px',
              border: 'none',
              bg: form.side === 'buy' ? '#f56c6c' : 'white',
              color: form.side === 'buy' ? 'white' : '#606266',
              fontSize: props.size === 'small' ? '12px' : '14px',
              cursor: 'pointer',
              fontWeight: form.side === 'buy' ? 'bold' : 'normal'
            })}
          >
            买入
          </button>
          <button
            onClick={() => setForm('side', 'sell')}
            class={css({
              flex: 1,
              padding: '8px',
              border: 'none',
              bg: form.side === 'sell' ? '#67c23a' : 'white',
              color: form.side === 'sell' ? 'white' : '#606266',
              fontSize: props.size === 'small' ? '12px' : '14px',
              cursor: 'pointer',
              fontWeight: form.side === 'sell' ? 'bold' : 'normal'
            })}
          >
            卖出
          </button>
        </div>

        {/* Order Type Toggle */}
        <div class={css({
          display: 'flex',
          marginBottom: '12px',
          gap: '4px'
        })}>
          <button
            onClick={() => setForm('orderType', 'market')}
            class={css({
              flex: 1,
              padding: '6px 8px',
              border: '1px solid',
              borderRadius: '4px',
              fontSize: props.size === 'small' ? '11px' : '12px',
              cursor: 'pointer',
              borderColor: form.orderType === 'market' ? '#409eff' : '#dcdfe6',
              bg: form.orderType === 'market' ? '#409eff' : 'white',
              color: form.orderType === 'market' ? 'white' : '#606266'
            })}
          >
            市价
          </button>
          <button
            onClick={() => setForm('orderType', 'limit')}
            class={css({
              flex: 1,
              padding: '6px 8px',
              border: '1px solid',
              borderRadius: '4px',
              fontSize: props.size === 'small' ? '11px' : '12px',
              cursor: 'pointer',
              borderColor: form.orderType === 'limit' ? '#409eff' : '#dcdfe6',
              bg: form.orderType === 'limit' ? '#409eff' : 'white',
              color: form.orderType === 'limit' ? 'white' : '#606266'
            })}
          >
            限价
          </button>
        </div>

        {/* Price Input (for limit orders) */}
        <Show when={form.orderType === 'limit'}>
          <div class={css({ marginBottom: '12px' })}>
            <input
              type="number"
              step="0.01"
              value={form.price || ''}
              onInput={(e) => setForm('price', Number(e.currentTarget.value))}
              placeholder="委托价格"
              class={css({
                width: '100%',
                padding: '6px 8px',
                border: '1px solid #dcdfe6',
                borderRadius: '4px',
                fontSize: props.size === 'small' ? '12px' : '14px',
                marginBottom: '6px'
              })}
            />
            
            {/* Price Adjustment Buttons */}
            <div class={css({
              display: 'flex',
              gap: '2px',
              justifyContent: 'space-between'
            })}>
              {priceAdjustments.map(adj => (
                <button
                  onClick={() => adjustPrice(adj.value)}
                  class={css({
                    padding: '3px 6px',
                    border: '1px solid #dcdfe6',
                    borderRadius: '3px',
                    fontSize: props.size === 'small' ? '10px' : '11px',
                    cursor: 'pointer',
                    bg: 'white',
                    color: '#606266'
                  })}
                >
                  {adj.label}
                </button>
              ))}
            </div>
          </div>
        </Show>

        {/* Quantity Input */}
        <div class={css({ marginBottom: '12px' })}>
          <input
            type="number"
            step="100"
            value={form.quantity}
            onInput={(e) => setForm('quantity', Number(e.currentTarget.value))}
            placeholder="数量"
            class={css({
              width: '100%',
              padding: '6px 8px',
              border: '1px solid #dcdfe6',
              borderRadius: '4px',
              fontSize: props.size === 'small' ? '12px' : '14px',
              marginBottom: '6px'
            })}
          />
          
          {/* Quantity Preset Buttons */}
          <div class={css({
            display: 'flex',
            gap: '2px',
            justifyContent: 'space-between'
          })}>
            {quantityPresets.map(qty => (
              <button
                onClick={() => setQuantity(qty)}
                class={css({
                  padding: '3px 6px',
                  border: '1px solid #dcdfe6',
                  borderRadius: '3px',
                  fontSize: props.size === 'small' ? '10px' : '11px',
                  cursor: 'pointer',
                  bg: form.quantity === qty ? '#409eff' : 'white',
                  color: form.quantity === qty ? 'white' : '#606266'
                })}
              >
                {qty}
              </button>
            ))}
          </div>
        </div>

        {/* Estimated Amount */}
        <div class={css({
          marginBottom: '12px',
          fontSize: props.size === 'small' ? '11px' : '12px',
          color: '#909399',
          textAlign: 'center'
        })}>
          预估金额: {formatCurrency(estimatedAmount())}
        </div>

        {/* Submit Button */}
        <button
          onClick={handleSubmit}
          disabled={!canSubmit()}
          class={css({
            width: '100%',
            padding: props.size === 'small' ? '8px' : '10px',
            border: 'none',
            borderRadius: '4px',
            fontSize: props.size === 'small' ? '12px' : '14px',
            fontWeight: 'bold',
            cursor: canSubmit() ? 'pointer' : 'not-allowed',
            bg: canSubmit() ? (form.side === 'buy' ? '#f56c6c' : '#67c23a') : '#c0c4cc',
            color: 'white',
            opacity: submitting() ? 0.7 : 1
          })}
        >
          {submitting() ? '提交中...' : `${form.side === 'buy' ? '买入' : '卖出'} ${props.symbol}`}
        </button>

        {/* Error Messages */}
        <Show when={Object.keys(errors).length > 0}>
          <div class={css({
            marginTop: '8px',
            padding: '6px',
            bg: '#fef2f2',
            border: '1px solid #fecaca',
            borderRadius: '4px',
            fontSize: props.size === 'small' ? '11px' : '12px',
            color: '#dc2626'
          })}>
            {Object.values(errors).join(', ')}
          </div>
        </Show>
      </div>
    </div>
  )
}

export default QuickOrderForm