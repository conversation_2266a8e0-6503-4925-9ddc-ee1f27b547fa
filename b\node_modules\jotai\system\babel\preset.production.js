System.register(["@babel/core","@babel/template"],function(d){"use strict";var s;return{setters:[null,function(l){s=l.default}],execute:function(){d("default",b);function l(e,i,t=[]){const o=[...u,...t];if(e.isIdentifier(i)&&o.includes(i.name))return!0;if(e.isMemberExpression(i)){const{property:a}=i;if(e.isIdentifier(a)&&o.includes(a.name))return!0}return!1}const u=["atom","atomFamily","atomWithDefault","atomWithObservable","atomWithReducer","atomWithReset","atomWithStorage","freezeAtom","loadable","selectAtom","splitAtom","unwrap","atomWithMachine","atomWithImmer","atomWithProxy","atomWithQuery","atomWithMutation","atomWithSubscription","atomWithStore","atomWithHash","atomWithLocation","focusAtom","atomWithValidate","validateAtoms","atomWithCache","atomWithRecoilValue"],f=s.default||s;function h({types:e},i){return{visitor:{ExportDefaultDeclaration(t,o){const{node:a}=t;if(e.isCallExpression(a.declaration)&&l(e,a.declaration.callee,i==null?void 0:i.customAtomNames)){const n=(o.filename||"unknown").replace(/\.\w+$/,"");let r=n.split("/").pop();r==="index"&&(r=n.slice(0,-6).split("/").pop()||"unknown");const c=f(`
          const %%atomIdentifier%% = %%atom%%;
          export default %%atomIdentifier%%
          `)({atomIdentifier:e.identifier(r),atom:a.declaration});t.replaceWithMultiple(c)}},VariableDeclarator(t){e.isIdentifier(t.node.id)&&e.isCallExpression(t.node.init)&&l(e,t.node.init.callee,i==null?void 0:i.customAtomNames)&&t.parentPath.insertAfter(e.expressionStatement(e.assignmentExpression("=",e.memberExpression(e.identifier(t.node.id.name),e.identifier("debugLabel")),e.stringLiteral(t.node.id.name))))}}}}const m=s.default||s;function p({types:e},i){return{pre({opts:t}){if(!t.filename)throw new Error("Filename must be available")},visitor:{Program:{exit(t){const o=m(`
          globalThis.jotaiAtomCache = globalThis.jotaiAtomCache || {
            cache: new Map(),
            get(name, inst) { 
              if (this.cache.has(name)) {
                return this.cache.get(name)
              }
              this.cache.set(name, inst)
              return inst
            },
          }`)();t.unshiftContainer("body",o)}},ExportDefaultDeclaration(t,o){const{node:a}=t;if(e.isCallExpression(a.declaration)&&l(e,a.declaration.callee,i==null?void 0:i.customAtomNames)){const n=`${o.filename||"unknown"}/defaultExport`,r=m("export default globalThis.jotaiAtomCache.get(%%atomKey%%, %%atom%%)")({atomKey:e.stringLiteral(n),atom:a.declaration});t.replaceWith(r)}},VariableDeclarator(t,o){var a,n;if(e.isIdentifier(t.node.id)&&e.isCallExpression(t.node.init)&&l(e,t.node.init.callee,i==null?void 0:i.customAtomNames)&&((a=t.parentPath.parentPath)!=null&&a.isProgram()||(n=t.parentPath.parentPath)!=null&&n.isExportNamedDeclaration())){const r=`${o.filename||"unknown"}/${t.node.id.name}`,c=m("const %%atomIdentifier%% = globalThis.jotaiAtomCache.get(%%atomKey%%, %%atom%%)")({atomIdentifier:e.identifier(t.node.id.name),atomKey:e.stringLiteral(r),atom:t.node.init});t.parentPath.replaceWith(c)}}}}}function b(e,i){return{plugins:[[h,i],[p,i]]}}}}});
