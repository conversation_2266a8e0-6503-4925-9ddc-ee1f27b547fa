
> quant-frontend@1.0.0 dev
> vite

Port 3000 is in use, trying another one...
Port 3001 is in use, trying another one...
Port 3002 is in use, trying another one...
Port 3003 is in use, trying another one...

  [32m[1mVITE[22m v5.4.19[39m  [2mready in [0m[1m646[22m[2m[0m ms[22m

  [32m➜[39m  [1mLocal[22m:   [36mhttp://localhost:[1m3004[22m/[39m
  [32m➜[39m  [1mNetwork[22m: [36mhttp://**********:[1m3004[22m/[39m
  [32m➜[39m  [1mNetwork[22m: [36mhttp://*************:[1m3004[22m/[39m
[2m12:30:02[22m [31m[1m[vite][22m[39m [31mhttp proxy error: /api/v1/errors[39m
Error: connect ECONNREFUSED ::1:8000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
[2m12:30:02[22m [31m[1m[vite][22m[39m [31mhttp proxy error: /api/v1/errors[39m
Error: connect ECONNREFUSED ::1:8000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
[2m12:31:00[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/AdvancedLayout.tsx[22m
[2m12:31:13[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/MetricCard.tsx[22m
[2m12:31:26[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/PortfolioOverview.tsx[22m
[2m12:31:38[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/MarketNews.tsx[22m
[2m12:31:49[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/pages/Dashboard.tsx[22m
[2m12:32:29[22m [36m[1m[vite][22m[39m [32mpage reload [39m[2msrc/index.tsx[22m
[2m12:33:37[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/AdvancedLayout.tsx[22m
[2m12:33:50[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/MetricCard.tsx[22m
[2m12:34:01[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/PortfolioOverview.tsx[22m
[2m12:34:13[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/MarketNews.tsx[22m
[2m12:34:25[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/pages/Dashboard.tsx[22m
[2m12:34:39[22m [36m[1m[vite][22m[39m [32mpage reload [39m[2msrc/index.tsx[22m
[2m12:39:16[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/AdvancedLayout.tsx[22m
[2m12:39:34[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/AdvancedLayout.tsx[22m
[2m12:40:16[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/AdvancedLayout.tsx[22m
[2m12:40:27[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/AdvancedLayout.tsx[22m
[2m12:40:43[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/AdvancedLayout.tsx[22m
[2m12:41:59[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/pages/Dashboard.tsx[22m
[2m12:42:31[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/pages/Dashboard.tsx[22m
[2m12:43:09[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/pages/Dashboard.tsx[22m
[2m16:49:23[22m [36m[1m[vite][22m[39m [32mpage reload [39m[2msrc/index.tsx[22m
[2m17:10:05[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/AdvancedLayout.tsx[22m
[2m17:10:20[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/AdvancedLayout.tsx[22m
[2m17:23:02[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/AdvancedLayout.tsx[22m
[2m17:23:03[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/MarketNews.tsx[22m
[2m17:23:03[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/MetricCard.tsx[22m
[2m17:23:03[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/PortfolioOverview.tsx[22m
[2m17:23:03[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/pages/Dashboard.tsx[22m
