import { createSignal, For, Show, createEffect } from 'solid-js'
import { css } from '../../styled-system/css'

interface Notification {
  id: string
  type: 'success' | 'warning' | 'error' | 'info' | 'trade' | 'system' | 'strategy'
  title: string
  message: string
  timestamp: string
  read: boolean
  priority: 'low' | 'medium' | 'high' | 'urgent'
  source: string
  actionRequired: boolean
  metadata?: {
    symbol?: string
    amount?: number
    strategy?: string
    orderId?: string
  }
}

interface NotificationSettings {
  enablePush: boolean
  enableEmail: boolean
  enableSMS: boolean
  tradeNotifications: boolean
  riskAlerts: boolean
  strategyUpdates: boolean
  systemNotifications: boolean
  priceAlerts: boolean
  marketNews: boolean
  quietHours: {
    enabled: boolean
    start: string
    end: string
  }
}

export default function NotificationCenter() {
  const [activeTab, setActiveTab] = createSignal<'all' | 'unread' | 'trade' | 'risk' | 'system'>('unread')
  const [selectedType, setSelectedType] = createSignal<string>('all')
  const [showSettings, setShowSettings] = createSignal(false)
  
  // 通知数据
  const [notifications, setNotifications] = createSignal<Notification[]>([
    {
      id: 'n001',
      type: 'trade',
      title: '交易成功',
      message: '000001.SZ 买入订单已成功执行，数量：1000股，价格：¥13.25',
      timestamp: '2024-01-15 14:32:15',
      read: false,
      priority: 'medium',
      source: '交易系统',
      actionRequired: false,
      metadata: {
        symbol: '000001.SZ',
        amount: 13250,
        orderId: 'ORD_20240115_001'
      }
    },
    {
      id: 'n002',
      type: 'warning',
      title: '风险预警',
      message: '投资组合集中度风险超过35%，建议适当分散持仓',
      timestamp: '2024-01-15 14:15:30',
      read: false,
      priority: 'high',
      source: '风险管理',
      actionRequired: true,
      metadata: {
        symbol: '000001.SZ'
      }
    },
    {
      id: 'n003',
      type: 'strategy',
      title: '策略信号',
      message: '动量策略触发买入信号：600519.SH，建议关注',
      timestamp: '2024-01-15 13:45:22',
      read: false,
      priority: 'medium',
      source: '策略引擎',
      actionRequired: true,
      metadata: {
        symbol: '600519.SH',
        strategy: '动量策略'
      }
    },
    {
      id: 'n004',
      type: 'error',
      title: '订单失败',
      message: '002415.SZ 卖出订单失败：资金不足',
      timestamp: '2024-01-15 13:20:08',
      read: true,
      priority: 'high',
      source: '交易系统',
      actionRequired: true,
      metadata: {
        symbol: '002415.SZ',
        orderId: 'ORD_20240115_002'
      }
    },
    {
      id: 'n005',
      type: 'info',
      title: '价格提醒',
      message: '300059.SZ 价格达到预设提醒点位：¥21.85',
      timestamp: '2024-01-15 12:30:15',
      read: true,
      priority: 'low',
      source: '价格监控',
      actionRequired: false,
      metadata: {
        symbol: '300059.SZ',
        amount: 21.85
      }
    },
    {
      id: 'n006',
      type: 'system',
      title: '系统升级',
      message: '交易系统将于今晚22:00-23:00进行升级维护',
      timestamp: '2024-01-15 11:00:00',
      read: true,
      priority: 'medium',
      source: '系统管理',
      actionRequired: false
    },
    {
      id: 'n007',
      type: 'success',
      title: '回测完成',
      message: '动量策略回测已完成，年化收益率：15.6%',
      timestamp: '2024-01-15 10:45:30',
      read: true,
      priority: 'low',
      source: '回测引擎',
      actionRequired: false,
      metadata: {
        strategy: '动量策略'
      }
    }
  ])

  // 通知设置
  const [settings, setSettings] = createSignal<NotificationSettings>({
    enablePush: true,
    enableEmail: false,
    enableSMS: true,
    tradeNotifications: true,
    riskAlerts: true,
    strategyUpdates: true,
    systemNotifications: true,
    priceAlerts: true,
    marketNews: false,
    quietHours: {
      enabled: true,
      start: '22:00',
      end: '08:00'
    }
  })

  const filteredNotifications = () => {
    let filtered = notifications()
    
    if (activeTab() === 'unread') {
      filtered = filtered.filter(n => !n.read)
    } else if (activeTab() === 'trade') {
      filtered = filtered.filter(n => n.type === 'trade' || n.type === 'error' && n.source === '交易系统')
    } else if (activeTab() === 'risk') {
      filtered = filtered.filter(n => n.type === 'warning' && n.source === '风险管理')
    } else if (activeTab() === 'system') {
      filtered = filtered.filter(n => n.type === 'system')
    }
    
    if (selectedType() !== 'all') {
      filtered = filtered.filter(n => n.type === selectedType())
    }
    
    return filtered.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
  }

  const unreadCount = () => notifications().filter(n => !n.read).length

  const getTypeIcon = (type: string) => {
    const icons: Record<string, string> = {
      success: '✅',
      warning: '⚠️',
      error: '❌',
      info: 'ℹ️',
      trade: '💰',
      system: '🔧',
      strategy: '📊'
    }
    return icons[type] || 'ℹ️'
  }

  const getTypeColor = (type: string) => {
    const colors: Record<string, string> = {
      success: '#52c41a',
      warning: '#faad14',
      error: '#ff4d4f',
      info: '#1890ff',
      trade: '#722ed1',
      system: '#8c8c8c',
      strategy: '#13c2c2'
    }
    return colors[type] || '#8c8c8c'
  }

  const getPriorityColor = (priority: string) => {
    const colors: Record<string, string> = {
      urgent: '#ff4d4f',
      high: '#fa8c16',
      medium: '#1890ff',
      low: '#52c41a'
    }
    return colors[priority] || '#8c8c8c'
  }

  const markAsRead = (id: string) => {
    setNotifications(prev => 
      prev.map(n => n.id === id ? { ...n, read: true } : n)
    )
  }

  const markAllAsRead = () => {
    setNotifications(prev => prev.map(n => ({ ...n, read: true })))
  }

  const deleteNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id))
  }

  const formatRelativeTime = (timestamp: string) => {
    const now = new Date()
    const notificationTime = new Date(timestamp)
    const diffMs = now.getTime() - notificationTime.getTime()
    const diffMins = Math.floor(diffMs / 60000)
    const diffHours = Math.floor(diffMins / 60)
    const diffDays = Math.floor(diffHours / 24)

    if (diffMins < 1) return '刚刚'
    if (diffMins < 60) return `${diffMins}分钟前`
    if (diffHours < 24) return `${diffHours}小时前`
    if (diffDays < 7) return `${diffDays}天前`
    return notificationTime.toLocaleDateString()
  }

  return (
    <div class={css({
      display: 'flex',
      'flex-direction': 'column',
      gap: '24px',
      padding: '24px'
    })}>
      {/* 顶部标题栏 */}
      <div class={css({
        display: 'flex',
        'align-items': 'center',
        'justify-content': 'space-between'
      })}>
        <div class={css({
          display: 'flex',
          'align-items': 'center',
          gap: '16px'
        })}>
          <h1 class={css({
            'font-size': '28px',
            'font-weight': '600',
            margin: 0,
            color: '#262626'
          })}>
            消息通知
          </h1>
          <Show when={unreadCount() > 0}>
            <div class={css({
              px: '8px',
              py: '4px',
              bg: '#ff4d4f',
              color: 'white',
              'border-radius': '12px',
              'font-size': '12px',
              'font-weight': '600'
            })}>
              {unreadCount()}条未读
            </div>
          </Show>
        </div>

        <div class={css({
          display: 'flex',
          gap: '12px'
        })}>
          <button
            onClick={markAllAsRead}
            class={css({
              px: '16px',
              py: '8px',
              bg: '#52c41a',
              color: 'white',
              border: 'none',
              'border-radius': '6px',
              'font-size': '14px',
              'font-weight': '500',
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              _hover: {
                bg: '#73d13d'
              }
            })}
          >
            全部已读
          </button>
          <button
            onClick={() => setShowSettings(!showSettings())}
            class={css({
              px: '16px',
              py: '8px',
              bg: '#1890ff',
              color: 'white',
              border: 'none',
              'border-radius': '6px',
              'font-size': '14px',
              'font-weight': '500',
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              _hover: {
                bg: '#40a9ff'
              }
            })}
          >
            设置
          </button>
        </div>
      </div>

      {/* 设置面板 */}
      <Show when={showSettings()}>
        <div class={css({
          bg: 'white',
          p: '24px',
          'border-radius': '12px',
          'box-shadow': '0 2px 8px rgba(0,0,0,0.1)',
          border: '1px solid #f0f0f0'
        })}>
          <h3 class={css({
            'font-size': '18px',
            'font-weight': '600',
            color: '#262626',
            mb: '20px'
          })}>
            通知设置
          </h3>

          <div class={css({
            display: 'grid',
            'grid-template-columns': 'repeat(auto-fit, minmax(250px, 1fr))',
            gap: '24px'
          })}>
            {/* 推送设置 */}
            <div>
              <h4 class={css({
                'font-size': '16px',
                'font-weight': '600',
                color: '#262626',
                mb: '12px'
              })}>
                推送方式
              </h4>
              <div class={css({
                display: 'flex',
                'flex-direction': 'column',
                gap: '8px'
              })}>
                {[
                  { key: 'enablePush', label: '浏览器推送' },
                  { key: 'enableEmail', label: '邮件通知' },
                  { key: 'enableSMS', label: '短信通知' }
                ].map(item => (
                  <label class={css({
                    display: 'flex',
                    'align-items': 'center',
                    gap: '8px',
                    'font-size': '14px',
                    cursor: 'pointer'
                  })}>
                    <input
                      type="checkbox"
                      checked={settings()[item.key as keyof NotificationSettings] as boolean}
                      onChange={(e) => setSettings(prev => ({
                        ...prev,
                        [item.key]: e.target.checked
                      }))}
                    />
                    {item.label}
                  </label>
                ))}
              </div>
            </div>

            {/* 通知类型 */}
            <div>
              <h4 class={css({
                'font-size': '16px',
                'font-weight': '600',
                color: '#262626',
                mb: '12px'
              })}>
                通知类型
              </h4>
              <div class={css({
                display: 'flex',
                'flex-direction': 'column',
                gap: '8px'
              })}>
                {[
                  { key: 'tradeNotifications', label: '交易通知' },
                  { key: 'riskAlerts', label: '风险预警' },
                  { key: 'strategyUpdates', label: '策略信号' },
                  { key: 'systemNotifications', label: '系统通知' },
                  { key: 'priceAlerts', label: '价格提醒' },
                  { key: 'marketNews', label: '市场资讯' }
                ].map(item => (
                  <label class={css({
                    display: 'flex',
                    'align-items': 'center',
                    gap: '8px',
                    'font-size': '14px',
                    cursor: 'pointer'
                  })}>
                    <input
                      type="checkbox"
                      checked={settings()[item.key as keyof NotificationSettings] as boolean}
                      onChange={(e) => setSettings(prev => ({
                        ...prev,
                        [item.key]: e.target.checked
                      }))}
                    />
                    {item.label}
                  </label>
                ))}
              </div>
            </div>

            {/* 免打扰时间 */}
            <div>
              <h4 class={css({
                'font-size': '16px',
                'font-weight': '600',
                color: '#262626',
                mb: '12px'
              })}>
                免打扰时间
              </h4>
              <div class={css({
                display: 'flex',
                'flex-direction': 'column',
                gap: '8px'
              })}>
                <label class={css({
                  display: 'flex',
                  'align-items': 'center',
                  gap: '8px',
                  'font-size': '14px',
                  cursor: 'pointer'
                })}>
                  <input
                    type="checkbox"
                    checked={settings().quietHours.enabled}
                    onChange={(e) => setSettings(prev => ({
                      ...prev,
                      quietHours: {
                        ...prev.quietHours,
                        enabled: e.target.checked
                      }
                    }))}
                  />
                  启用免打扰
                </label>
                <Show when={settings().quietHours.enabled}>
                  <div class={css({
                    display: 'flex',
                    'align-items': 'center',
                    gap: '8px',
                    'font-size': '14px'
                  })}>
                    <span>从</span>
                    <input
                      type="time"
                      value={settings().quietHours.start}
                      onChange={(e) => setSettings(prev => ({
                        ...prev,
                        quietHours: {
                          ...prev.quietHours,
                          start: e.target.value
                        }
                      }))}
                      class={css({
                        px: '8px',
                        py: '4px',
                        border: '1px solid #d9d9d9',
                        'border-radius': '4px',
                        'font-size': '12px'
                      })}
                    />
                    <span>到</span>
                    <input
                      type="time"
                      value={settings().quietHours.end}
                      onChange={(e) => setSettings(prev => ({
                        ...prev,
                        quietHours: {
                          ...prev.quietHours,
                          end: e.target.value
                        }
                      }))}
                      class={css({
                        px: '8px',
                        py: '4px',
                        border: '1px solid #d9d9d9',
                        'border-radius': '4px',
                        'font-size': '12px'
                      })}
                    />
                  </div>
                </Show>
              </div>
            </div>
          </div>
        </div>
      </Show>

      {/* 筛选栏 */}
      <div class={css({
        display: 'flex',
        'align-items': 'center',
        'justify-content': 'space-between',
        bg: 'white',
        p: '16px',
        'border-radius': '12px',
        'box-shadow': '0 2px 8px rgba(0,0,0,0.1)'
      })}>
        <div class={css({
          display: 'flex',
          gap: '4px'
        })}>
          {[
            { key: 'unread', label: '未读', count: unreadCount() },
            { key: 'all', label: '全部', count: notifications().length },
            { key: 'trade', label: '交易', count: notifications().filter(n => n.type === 'trade').length },
            { key: 'risk', label: '风险', count: notifications().filter(n => n.type === 'warning').length },
            { key: 'system', label: '系统', count: notifications().filter(n => n.type === 'system').length }
          ].map(tab => (
            <button
              class={css({
                px: '16px',
                py: '8px',
                border: 'none',
                bg: activeTab() === tab.key ? '#1890ff' : 'transparent',
                color: activeTab() === tab.key ? 'white' : '#666',
                'border-radius': '6px',
                'font-size': '14px',
                'font-weight': activeTab() === tab.key ? '600' : '400',
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                _hover: {
                  bg: activeTab() === tab.key ? '#40a9ff' : '#f0f0f0'
                }
              })}
              onClick={() => setActiveTab(tab.key as any)}
            >
              {tab.label}
              <Show when={tab.count > 0}>
                <span class={css({
                  ml: '6px',
                  px: '4px',
                  py: '1px',
                  bg: activeTab() === tab.key ? 'rgba(255,255,255,0.3)' : '#ff4d4f',
                  color: activeTab() === tab.key ? 'white' : 'white',
                  'border-radius': '8px',
                  'font-size': '10px',
                  'font-weight': '600'
                })}>
                  {tab.count}
                </span>
              </Show>
            </button>
          ))}
        </div>

        <select
          value={selectedType()}
          onChange={(e) => setSelectedType(e.target.value)}
          class={css({
            px: '12px',
            py: '8px',
            border: '1px solid #d9d9d9',
            'border-radius': '6px',
            'font-size': '14px'
          })}
        >
          <option value="all">所有类型</option>
          <option value="trade">交易</option>
          <option value="warning">风险</option>
          <option value="strategy">策略</option>
          <option value="info">信息</option>
          <option value="system">系统</option>
        </select>
      </div>

      {/* 通知列表 */}
      <div class={css({
        display: 'flex',
        'flex-direction': 'column',
        gap: '12px'
      })}>
        <Show when={filteredNotifications().length === 0}>
          <div class={css({
            bg: 'white',
            p: '48px',
            'border-radius': '12px',
            'text-align': 'center',
            'box-shadow': '0 2px 8px rgba(0,0,0,0.1)'
          })}>
            <div class={css({
              'font-size': '48px',
              mb: '16px'
            })}>
              📬
            </div>
            <h3 class={css({
              'font-size': '18px',
              'font-weight': '600',
              color: '#8c8c8c',
              mb: '8px'
            })}>
              暂无通知
            </h3>
            <p class={css({
              'font-size': '14px',
              color: '#bfbfbf'
            })}>
              当前分类下没有消息通知
            </p>
          </div>
        </Show>

        <For each={filteredNotifications()}>
          {(notification) => (
            <div class={css({
              bg: 'white',
              p: '20px',
              'border-radius': '12px',
              boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
              border: notification.read ? '1px solid #f0f0f0' : '1px solid #1890ff',
              opacity: notification.read ? 0.8 : 1,
              position: 'relative',
              transition: 'all 0.3s ease',
              _hover: {
                transform: 'translateY(-2px)',
                'box-shadow': '0 4px 16px rgba(0,0,0,0.15)'
              }
            })}>
              {/* 优先级指示器 */}
              <div class={css({
                position: 'absolute',
                left: 0,
                top: 0,
                bottom: 0,
                w: '4px',
                bg: getPriorityColor(notification.priority),
                'border-radius': '2px 0 0 2px'
              })}></div>

              <div class={css({
                display: 'flex',
                'align-items': 'flex-start',
                gap: '16px'
              })}>
                {/* 类型图标 */}
                <div class={css({
                  'font-size': '20px',
                  'min-width': '20px',
                  'text-align': 'center'
                })}>
                  {getTypeIcon(notification.type)}
                </div>

                {/* 内容区 */}
                <div class={css({
                  flex: 1,
                  'min-width': 0
                })}>
                  <div class={css({
                    display: 'flex',
                    'align-items': 'center',
                    'justify-content': 'space-between',
                    mb: '8px'
                  })}>
                    <div class={css({
                      display: 'flex',
                      'align-items': 'center',
                      gap: '8px'
                    })}>
                      <h4 class={css({
                        'font-size': '16px',
                        'font-weight': '600',
                        color: '#262626',
                        margin: 0
                      })}>
                        {notification.title}
                      </h4>
                      <span class={css({
                        px: '6px',
                        py: '2px',
                        bg: getTypeColor(notification.type) + '20',
                        color: getTypeColor(notification.type),
                        'border-radius': '4px',
                        'font-size': '10px',
                        'font-weight': '600'
                      })}>
                        {notification.source}
                      </span>
                      <Show when={notification.actionRequired}>
                        <span class={css({
                          px: '6px',
                          py: '2px',
                          bg: '#ff4d4f20',
                          color: '#ff4d4f',
                          'border-radius': '4px',
                          'font-size': '10px',
                          'font-weight': '600'
                        })}>
                          需处理
                        </span>
                      </Show>
                    </div>

                    <div class={css({
                      display: 'flex',
                      'align-items': 'center',
                      gap: '8px'
                    })}>
                      <span class={css({
                        'font-size': '12px',
                        color: '#8c8c8c'
                      })}>
                        {formatRelativeTime(notification.timestamp)}
                      </span>
                      <Show when={!notification.read}>
                        <div class={css({
                          w: '8px',
                          h: '8px',
                          bg: '#1890ff',
                          'border-radius': '50%'
                        })}></div>
                      </Show>
                    </div>
                  </div>

                  <p class={css({
                    'font-size': '14px',
                    color: '#595959',
                    'line-height': '1.5',
                    margin: 0,
                    mb: '12px'
                  })}>
                    {notification.message}
                  </p>

                  {/* 元数据 */}
                  <Show when={notification.metadata}>
                    <div class={css({
                      display: 'flex',
                      'flex-wrap': 'wrap',
                      gap: '8px',
                      mb: '12px'
                    })}>
                      <Show when={notification.metadata?.symbol}>
                        <span class={css({
                          px: '6px',
                          py: '2px',
                          bg: '#f0f0f0',
                          color: '#595959',
                          'border-radius': '4px',
                          'font-size': '10px'
                        })}>
                          {notification.metadata?.symbol}
                        </span>
                      </Show>
                      <Show when={notification.metadata?.amount}>
                        <span class={css({
                          px: '6px',
                          py: '2px',
                          bg: '#f0f0f0',
                          color: '#595959',
                          'border-radius': '4px',
                          'font-size': '10px'
                        })}>
                          ¥{notification.metadata?.amount?.toLocaleString()}
                        </span>
                      </Show>
                      <Show when={notification.metadata?.strategy}>
                        <span class={css({
                          px: '6px',
                          py: '2px',
                          bg: '#f0f0f0',
                          color: '#595959',
                          'border-radius': '4px',
                          'font-size': '10px'
                        })}>
                          {notification.metadata?.strategy}
                        </span>
                      </Show>
                    </div>
                  </Show>

                  {/* 操作按钮 */}
                  <div class={css({
                    display: 'flex',
                    gap: '8px'
                  })}>
                    <Show when={!notification.read}>
                      <button
                        onClick={() => markAsRead(notification.id)}
                        class={css({
                          px: '12px',
                          py: '6px',
                          bg: '#1890ff',
                          color: 'white',
                          border: 'none',
                          'border-radius': '4px',
                          'font-size': '12px',
                          cursor: 'pointer',
                          transition: 'all 0.3s ease',
                          _hover: {
                            bg: '#40a9ff'
                          }
                        })}
                      >
                        标记已读
                      </button>
                    </Show>
                    <Show when={notification.actionRequired}>
                      <button class={css({
                        px: '12px',
                        py: '6px',
                        bg: '#52c41a',
                        color: 'white',
                        border: 'none',
                        'border-radius': '4px',
                        'font-size': '12px',
                        cursor: 'pointer',
                        transition: 'all 0.3s ease',
                        _hover: {
                          bg: '#73d13d'
                        }
                      })}>
                        立即处理
                      </button>
                    </Show>
                    <button
                      onClick={() => deleteNotification(notification.id)}
                      class={css({
                        px: '12px',
                        py: '6px',
                        bg: '#ff4d4f',
                        color: 'white',
                        border: 'none',
                        'border-radius': '4px',
                        'font-size': '12px',
                        cursor: 'pointer',
                        transition: 'all 0.3s ease',
                        _hover: {
                          bg: '#ff7875'
                        }
                      })}
                    >
                      删除
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </For>
      </div>
    </div>
  )
}