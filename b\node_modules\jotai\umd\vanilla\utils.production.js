!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports,require("jotai/vanilla")):"function"==typeof define&&define.amd?define(["exports","jotai/vanilla"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).jotaiVanillaUtils={},t.jotaiVanilla)}(this,function(t,n){"use strict";var e=Symbol("");function r(t,n){(null==n||n>t.length)&&(n=t.length);for(var e=0,r=Array(n);e<n;e++)r[e]=t[e];return r}function o(t,n){var e="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(e)return(e=e.call(t)).next.bind(e);if(Array.isArray(t)||(e=function(t,n){if(t){if("string"==typeof t)return r(t,n);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?r(t,n):void 0}}(t))||n){e&&(t=e);var o=0;return function(){return o>=t.length?{done:!0}:{done:!1,value:t[o++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function i(){return i=Object.assign?Object.assign.bind():function(t){for(var n=1;n<arguments.length;n++){var e=arguments[n];for(var r in e)({}).hasOwnProperty.call(e,r)&&(t[r]=e[r])}return t},i.apply(null,arguments)}var a=function(t,n,e){return(n.has(e)?n:n.set(e,t())).get(e)},u=new WeakMap;var f=new WeakSet,c=function(t){if("object"!=typeof t||null===t)return t;Object.freeze(t);for(var n,e=o(Object.getOwnPropertyNames(t));!(n=e()).done;){var r=n.value;c(t[r])}return t};function l(t){if(f.has(t))return t;f.add(t);var n=t.read;if(t.read=function(t,e){return c(n.call(this,t,e))},"write"in t){var e=t.write;t.write=function(n,r){for(var o=arguments.length,i=new Array(o>2?o-2:0),a=2;a<o;a++)i[a-2]=arguments[a];return e.call.apply(e,[this,n,function(){for(var n=arguments.length,e=new Array(n),o=0;o<n;o++)e[o]=arguments[o];return e[0]===t&&(e[1]=c(e[1])),r.apply(void 0,e)}].concat(i))}}return t}var v=function(t,n,e){return(n.has(e)?n:n.set(e,t())).get(e)},s=new WeakMap,d={},m=function(t){return!!t.write};var h=function(t){return"function"==typeof(null==t?void 0:t.then)};function p(t,n){var e,r;void 0===t&&(t=function(){try{return window.localStorage}catch(t){return}});var o,i={getItem:function(o,i){var a,u,f=function(t){if(e!==(t=t||"")){try{r=JSON.parse(t,null==n?void 0:n.reviver)}catch(t){return i}e=t}return r},c=null!=(a=null==(u=t())?void 0:u.getItem(o))?a:null;return h(c)?c.then(f):f(c)},setItem:function(e,r){var o;return null==(o=t())?void 0:o.setItem(e,JSON.stringify(r,null==n?void 0:n.replacer))},removeItem:function(n){var e;return null==(e=t())?void 0:e.removeItem(n)}};try{var a;o=null==(a=t())?void 0:a.subscribe}catch(t){}return!o&&"undefined"!=typeof window&&"function"==typeof window.addEventListener&&window.Storage&&(o=function(n,e){if(!(t()instanceof window.Storage))return function(){};var r=function(r){r.storageArea===t()&&r.key===n&&e(r.newValue)};return window.addEventListener("storage",r),function(){window.removeEventListener("storage",r)}}),o&&(i.subscribe=function(t){return function(n,e,r){return t(n,function(t){var n;try{n=JSON.parse(t||"")}catch(t){n=r}e(n)})}}(o)),i}var y=p();var b=new WeakMap,w={state:"loading"};var g=function(t,n,e){return(n.has(e)?n:n.set(e,t())).get(e)},O=new WeakMap,k=function(){};t.RESET=e,t.atomFamily=function(t,n){var e=null,r=new Map,i=new Set,a=function(i){var f;if(void 0===n)f=r.get(i);else for(var c,l=o(r);!(c=l()).done;){var v=c.value,s=v[0],d=v[1];if(n(s,i)){f=d;break}}if(void 0!==f){if(null==e||!e(f[1],i))return f[0];a.remove(i)}var m=t(i);return r.set(i,[m,Date.now()]),u("CREATE",i,m),m},u=function(t,n,e){for(var r,a=o(i);!(r=a()).done;){(0,r.value)({type:t,param:n,atom:e})}};return a.unstable_listen=function(t){return i.add(t),function(){i.delete(t)}},a.getParams=function(){return r.keys()},a.remove=function(t){if(void 0===n){if(!r.has(t))return;var e=r.get(t)[0];r.delete(t),u("REMOVE",t,e)}else for(var i,a=o(r);!(i=a()).done;){var f=i.value,c=f[0],l=f[1][0];if(n(c,t)){r.delete(c),u("REMOVE",c,l);break}}},a.setShouldRemove=function(t){if(e=t)for(var n,i=o(r);!(n=i()).done;){var a=n.value,f=a[0],c=a[1],l=c[0],v=c[1];e(v,f)&&(r.delete(f),u("REMOVE",f,l))}},a},t.atomWithDefault=function(t){var r=Symbol(),o=n.atom(r),i=n.atom(function(n,e){var i=n(o);return i!==r?i:t(n,e)},function(t,n,a){if(a===e)n(o,r);else if("function"==typeof a){var u=t(i);n(o,a(u))}else n(o,a)});return i},t.atomWithLazy=function(t){var e=n.atom(void 0);return delete e.init,Object.defineProperty(e,"init",{get:function(){return t()}}),e},t.atomWithObservable=function(t,e){var r=function(t){if("e"in t)throw t.e;return t.d},o=n.atom(function(r){var o,i,a,u=t(r),f=null==(o=(i=u)[Symbol.observable])?void 0:o.call(i);f&&(u=f);var c,l,v,s,d=function(){return new Promise(function(t){a=t})},m=e&&"initialValue"in e?{d:"function"==typeof e.initialValue?e.initialValue():e.initialValue}:d(),h=function(t){l=t,null==a||a(t),null==c||c(t)},p=function(){return!c},y=function(){v&&(v.unsubscribe(),v=void 0)},b=function(){v&&(clearTimeout(s),v.unsubscribe()),v=u.subscribe({next:function(t){return h({d:t})},error:function(t){return h({e:t})},complete:function(){}}),p()&&null!=e&&e.unstable_timeout&&(s=setTimeout(y,e.unstable_timeout))};b();var w=n.atom(l||m);return w.onMount=function(t){return c=t,l&&t(l),v?clearTimeout(s):b(),function(){c=void 0,null!=e&&e.unstable_timeout?s=setTimeout(y,e.unstable_timeout):y()}},[w,u,d,b,p]});return n.atom(function(t){var n,e=t(o),i=t(e[0]);return"function"==typeof(null==(n=i)?void 0:n.then)?i.then(r):r(i)},function(t,n,e){var r=t(o),i=r[0],a=r[1],u=r[2],f=r[3],c=r[4];if(!("next"in a))throw new Error("observable is not subject");c()&&(n(i,u()),f()),a.next(e)})},t.atomWithReducer=function(t,e){return n.atom(t,function(t,n,r){n(this,e(t(this),r))})},t.atomWithRefresh=function(t,e){var r=n.atom(0);return n.atom(function(n,e){return n(r),t(n,e)},function(t,n){for(var o=arguments.length,i=new Array(o>2?o-2:0),a=2;a<o;a++)i[a-2]=arguments[a];if(0===i.length)n(r,function(t){return t+1});else if(e)return e.apply(void 0,[t,n].concat(i))})},t.atomWithReset=function(t){var r=n.atom(t,function(n,o,i){var a="function"==typeof i?i(n(r)):i;o(r,a===e?t:a)});return r},t.atomWithStorage=function(t,r,o,i){void 0===o&&(o=y);var a=null==i?void 0:i.getOnInit,u=n.atom(a?o.getItem(t,r):r);return u.onMount=function(n){var e;return n(o.getItem(t,r)),o.subscribe&&(e=o.subscribe(t,n,r)),e},n.atom(function(t){return t(u)},function(n,i,a){var f="function"==typeof a?a(n(u)):a;return f===e?(i(u,r),o.removeItem(t)):h(f)?f.then(function(n){return i(u,n),o.setItem(t,n)}):(i(u,f),o.setItem(t,f))})},t.createJSONStorage=p,t.freezeAtom=l,t.freezeAtomCreator=function(t){return function(){return l(t.apply(void 0,arguments))}},t.loadable=function(t){return e=function(){var e=new WeakMap,r=n.atom(0),o=n.atom(function(n,o){var i,a,u=o.setSelf;n(r);try{i=n(t)}catch(t){return{state:"hasError",error:t}}if("function"!=typeof(null==(a=i)?void 0:a.then))return{state:"hasData",data:i};var f=i,c=e.get(f);if(c)return c;f.then(function(t){e.set(f,{state:"hasData",data:t}),u()},function(t){e.set(f,{state:"hasError",error:t}),u()});var l=e.get(f);return l||(e.set(f,w),w)},function(t,n){n(r,function(t){return t+1})});return n.atom(function(t){return t(o)})},r=t,(b.has(r)?b:b.set(r,e())).get(r);var e,r},t.selectAtom=function(t,e,r){return void 0===r&&(r=Object.is),o=function(){var o=Symbol(),i=n.atom(function(n){var a=n(i);return function(t){var n=t[0],i=t[1];if(i===o)return e(n);var a=e(n,i);return r(i,a)?i:a}([n(t),a])});return i.init=o,i},i=e,f=r,c=a(function(){return new WeakMap},u,t),l=a(function(){return new WeakMap},c,i),a(o,l,f);var o,i,f,c,l},t.splitAtom=function(t,e){return r=function(){var r=new WeakMap,o=function(a,u){var f=r.get(a);if(f)return f;var c=u&&r.get(u),l=[],v=[];return a.forEach(function(r,u){var f=e?e(r):u;v[u]=f;var s=c&&c.atomList[c.keyList.indexOf(f)];if(s)l[u]=s;else{var d=function(n){var e=n(i),r=n(t),u=o(r,null==e?void 0:e.arr).keyList.indexOf(f);if(u<0||u>=r.length){var c=a[o(a).keyList.indexOf(f)];if(c)return c;throw new Error("splitAtom: index out of bounds for read")}return r[u]};l[u]=m(t)?n.atom(d,function(n,e,r){var a=n(i),u=n(t),c=o(u,null==a?void 0:a.arr).keyList.indexOf(f);if(c<0||c>=u.length)throw new Error("splitAtom: index out of bounds for write");var l="function"==typeof r?r(u[c]):r;Object.is(u[c],l)||e(t,[].concat(u.slice(0,c),[l],u.slice(c+1)))}):n.atom(d)}}),f=c&&c.keyList.length===v.length&&c.keyList.every(function(t,n){return t===v[n]})?c:{arr:a,atomList:l,keyList:v},r.set(a,f),f},i=n.atom(function(n){var e=n(i),r=n(t);return o(r,null==e?void 0:e.arr)});i.init=void 0;var a=m(t)?n.atom(function(t){return t(i).atomList},function(n,e,r){switch(r.type){case"remove":var o=n(a).indexOf(r.atom);if(o>=0){var i=n(t);e(t,[].concat(i.slice(0,o),i.slice(o+1)))}break;case"insert":var u=r.before?n(a).indexOf(r.before):n(a).length;if(u>=0){var f=n(t);e(t,[].concat(f.slice(0,u),[r.value],f.slice(u)))}break;case"move":var c=n(a).indexOf(r.atom),l=r.before?n(a).indexOf(r.before):n(a).length;if(c>=0&&l>=0){var v=n(t);e(t,c<l?[].concat(v.slice(0,c),v.slice(c+1,l),[v[c]],v.slice(l)):[].concat(v.slice(0,l),[v[c]],v.slice(l,c),v.slice(c+1)))}}}):n.atom(function(t){return t(i).atomList});return a},o=e||d,i=v(function(){return new WeakMap},s,t),v(r,i,o);var r,o,i},t.unstable_withStorageValidator=function(t){return function(n){return i({},n,{getItem:function(e,r){var o=function(n){return t(n)?n:r},i=n.getItem(e,r);return h(i)?i.then(o):o(i)}})}},t.unwrap=function(t,e){return void 0===e&&(e=k),r=function(){var r=new WeakMap,o=new WeakMap,i=n.atom(0),a=n.atom(function(n,u){var f=u.setSelf;n(i);var c,l=n(a),v=n(t);if("function"!=typeof(null==(c=v)?void 0:c.then))return{v:v};if(v!==(null==l?void 0:l.p)&&v.then(function(t){o.set(v,t),f()},function(t){r.set(v,t),f()}),r.has(v))throw r.get(v);return o.has(v)?{p:v,v:o.get(v)}:l&&"v"in l?{p:v,f:e(l.v),v:l.v}:{p:v,f:e()}},function(t,n){n(i,function(t){return t+1})});return a.init=void 0,n.atom(function(t){var n=t(a);return"f"in n?n.f:n.v},function(n,e){for(var r=arguments.length,o=new Array(r>2?r-2:0),i=2;i<r;i++)o[i-2]=arguments[i];return e.apply(void 0,[t].concat(o))})},o=e,i=g(function(){return new WeakMap},O,t),g(r,i,o);var r,o,i}});
