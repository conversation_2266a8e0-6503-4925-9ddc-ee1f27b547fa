/**
 * SolidJS + <PERSON><PERSON> 状态管理
 * 核心状态原子定义
 */

import { atom } from 'jotai'

// 用户相关状态
export interface User {
  id: string;
  username: string;
  email: string;
  nickname: string;
  avatar?: string;
  phone?: string;
  role: 'admin' | 'user' | 'trader';
  status: 'active' | 'inactive';
  createdAt: string;
  updatedAt: string;
  lastLoginAt: string;
  preferences: {
    theme: 'light' | 'dark';
    language: 'zh-CN' | 'en-US';
    timezone: string;
    notifications: {
      email: boolean;
      push: boolean;
      sms: boolean;
    };
    trading: {
      confirmOrders: boolean;
      showRiskWarnings: boolean;
      defaultOrderType: 'market' | 'limit';
    };
  };
  profile: {
    realName: string;
    gender: 'male' | 'female';
    investmentExperience: 'beginner' | 'intermediate' | 'expert';
    riskTolerance: 'conservative' | 'moderate' | 'aggressive';
  };
}

export interface UserState {
  user: User | null;
  isAuthenticated: boolean;
  loading: boolean;
  error: string | null;
}

// 用户状态原子
export const userAtom = atom<UserState>({
  user: null,
  isAuthenticated: false,
  loading: false,
  error: null
})

// 交易相关状态
export interface TradingState {
  account: {
    id: string;
    availableCash: number;
    totalAssets: number;
    dayPnl: number;
  } | null;
  orders: any[];
  positions: any[];
  trades: any[];
  isConnected: boolean;
  loading: boolean;
  error: string | null;
}

// 交易状态原子
export const tradingAtom = atom<TradingState>({
  account: null,
  orders: [],
  positions: [],
  trades: [],
  isConnected: false,
  loading: false,
  error: null
})

// 市场数据状态
export interface MarketDataState {
  realTime: Record<string, {
    symbol: string;
    price: number;
    change: number;
    changePercent: number;
    volume: number;
    timestamp: number;
  }>;
  charts: Record<string, any[]>;
  watchlist: string[];
  loading: boolean;
  error: string | null;
}

// 市场数据原子
export const marketDataAtom = atom<MarketDataState>({
  realTime: {},
  charts: {},
  watchlist: [],
  loading: false,
  error: null
})

// 通知相关状态
export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  timestamp: number;
  read: boolean;
}

export interface NotificationState {
  notifications: Notification[];
  unreadCount: number;
}

export const notificationAtom = atom<NotificationState>({
  notifications: [],
  unreadCount: 0
})

// 策略相关状态
export interface Strategy {
  id: string;
  name: string;
  description: string;
  type: 'trend' | 'mean_reversion' | 'arbitrage' | 'custom';
  status: 'draft' | 'active' | 'paused' | 'stopped';
  code: string;
  parameters: Record<string, any>;
  performance: {
    totalReturn: number;
    sharpeRatio: number;
    maxDrawdown: number;
    winRate: number;
  };
  createdAt: string;
  updatedAt: string;
}

export interface StrategyState {
  strategies: Strategy[];
  activeStrategy: Strategy | null;
  loading: boolean;
  error: string | null;
}

export const strategyAtom = atom<StrategyState>({
  strategies: [],
  activeStrategy: null,
  loading: false,
  error: null
})

// 回测相关状态
export interface BacktestResult {
  id: string;
  strategyId: string;
  status: 'running' | 'completed' | 'failed';
  startDate: string;
  endDate: string;
  initialCapital: number;
  finalCapital: number;
  totalReturn: number;
  sharpeRatio: number;
  maxDrawdown: number;
  trades: number;
  winRate: number;
  createdAt: string;
}

export interface BacktestState {
  results: BacktestResult[];
  currentResult: BacktestResult | null;
  isRunning: boolean;
  loading: boolean;
  error: string | null;
}

export const backtestAtom = atom<BacktestState>({
  results: [],
  currentResult: null,
  isRunning: false,
  loading: false,
  error: null
})

// 应用主题状态
export interface ThemeState {
  mode: 'light' | 'dark';
  primaryColor: string;
  fontSize: 'small' | 'medium' | 'large';
}

export const themeAtom = atom<ThemeState>({
  mode: 'light',
  primaryColor: '#409eff',
  fontSize: 'medium'
})

// WebSocket连接状态
export type WSConnectionStatus = 'connecting' | 'connected' | 'disconnected' | 'error';

export interface ConnectionState {
  trading: WSConnectionStatus;
  marketData: WSConnectionStatus;
  lastConnectedAt: string | null;
}

export const connectionAtom = atom<ConnectionState>({
  trading: 'disconnected',
  marketData: 'disconnected',
  lastConnectedAt: null
})

// 模态框状态
export interface ModalState {
  isOpen: boolean;
  type: string;
  data?: any;
}

export const modalAtom = atom<ModalState>({
  isOpen: false,
  type: '',
  data: null
})
