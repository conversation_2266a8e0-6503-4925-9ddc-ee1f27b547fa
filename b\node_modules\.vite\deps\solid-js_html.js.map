{"version": 3, "sources": ["../../solid-js/html/dist/html.js"], "sourcesContent": ["import { effect, style, insert, untrack, spread, createComponent, delegateEvents, classList, mergeProps, dynamicProperty, setAttribute, setAttributeNS, addEventListener, Aliases, getPropAlias, Properties, ChildProperties, DelegatedEvents, SVGElements, SVGNamespace } from 'solid-js/web';\n\nconst tagRE = /(?:<!--[\\S\\s]*?-->|<(?:\"[^\"]*\"['\"]*|'[^']*'['\"]*|[^'\">])+>)/g;\nconst attrRE = /(?:\\s(?<boolean>[^/\\s><=]+?)(?=[\\s/>]))|(?:(?<name>\\S+?)(?:\\s*=\\s*(?:(['\"])(?<quotedValue>[\\s\\S]*?)\\3|(?<unquotedValue>[^\\s>]+))))/g;\nconst lookup = {\n  area: true,\n  base: true,\n  br: true,\n  col: true,\n  embed: true,\n  hr: true,\n  img: true,\n  input: true,\n  keygen: true,\n  link: true,\n  menuitem: true,\n  meta: true,\n  param: true,\n  source: true,\n  track: true,\n  wbr: true\n};\nfunction parseTag(tag) {\n  const res = {\n    type: 'tag',\n    name: '',\n    voidElement: false,\n    attrs: [],\n    children: []\n  };\n  const tagMatch = tag.match(/<\\/?([^\\s]+?)[/\\s>]/);\n  if (tagMatch) {\n    res.name = tagMatch[1];\n    if (lookup[tagMatch[1].toLowerCase()] || tag.charAt(tag.length - 2) === '/') {\n      res.voidElement = true;\n    }\n    if (res.name.startsWith('!--')) {\n      const endIndex = tag.indexOf('-->');\n      return {\n        type: 'comment',\n        comment: endIndex !== -1 ? tag.slice(4, endIndex) : ''\n      };\n    }\n  }\n  const reg = new RegExp(attrRE);\n  for (const match of tag.matchAll(reg)) {\n    if ((match[1] || match[2]).startsWith('use:')) {\n      res.attrs.push({\n        type: 'directive',\n        name: match[1] || match[2],\n        value: match[4] || match[5] || ''\n      });\n    } else {\n      res.attrs.push({\n        type: 'attr',\n        name: match[1] || match[2],\n        value: match[4] || match[5] || ''\n      });\n    }\n  }\n  return res;\n}\nfunction pushTextNode(list, html, start) {\n  const end = html.indexOf('<', start);\n  const content = html.slice(start, end === -1 ? undefined : end);\n  if (!/^\\s*$/.test(content)) {\n    list.push({\n      type: 'text',\n      content: content\n    });\n  }\n}\nfunction pushCommentNode(list, tag) {\n  const content = tag.replace('<!--', '').replace('-->', '');\n  if (!/^\\s*$/.test(content)) {\n    list.push({\n      type: 'comment',\n      content: content\n    });\n  }\n}\nfunction parse(html) {\n  const result = [];\n  let current = undefined;\n  let level = -1;\n  const arr = [];\n  const byTag = {};\n  html.replace(tagRE, (tag, index) => {\n    const isOpen = tag.charAt(1) !== '/';\n    const isComment = tag.slice(0, 4) === '<!--';\n    const start = index + tag.length;\n    const nextChar = html.charAt(start);\n    let parent = undefined;\n    if (isOpen && !isComment) {\n      level++;\n      current = parseTag(tag);\n      if (!current.voidElement && nextChar && nextChar !== '<') {\n        pushTextNode(current.children, html, start);\n      }\n      byTag[current.tagName] = current;\n      if (level === 0) {\n        result.push(current);\n      }\n      parent = arr[level - 1];\n      if (parent) {\n        parent.children.push(current);\n      }\n      arr[level] = current;\n    }\n    if (isComment) {\n      if (level < 0) {\n        pushCommentNode(result, tag);\n      } else {\n        pushCommentNode(arr[level].children, tag);\n      }\n    }\n    if (isComment || !isOpen || current.voidElement) {\n      if (!isComment) {\n        level--;\n      }\n      if (nextChar !== '<' && nextChar) {\n        parent = level === -1 ? result : arr[level].children;\n        pushTextNode(parent, html, start);\n      }\n    }\n  });\n  return result;\n}\nfunction attrString(attrs) {\n  const buff = [];\n  for (const attr of attrs) {\n    buff.push(attr.name + '=\"' + attr.value.replace(/\"/g, '&quot;') + '\"');\n  }\n  if (!buff.length) {\n    return '';\n  }\n  return ' ' + buff.join(' ');\n}\nfunction stringifier(buff, doc) {\n  switch (doc.type) {\n    case 'text':\n      return buff + doc.content;\n    case 'tag':\n      buff += '<' + doc.name + (doc.attrs ? attrString(doc.attrs) : '') + (doc.voidElement ? '/>' : '>');\n      if (doc.voidElement) {\n        return buff;\n      }\n      return buff + doc.children.reduce(stringifier, '') + '</' + doc.name + '>';\n    case 'comment':\n      return buff += '<!--' + doc.content + '-->';\n  }\n}\nfunction stringify(doc) {\n  return doc.reduce(function (token, rootEl) {\n    return token + stringifier('', rootEl);\n  }, '');\n}\nconst cache = new Map();\nconst VOID_ELEMENTS = /^(?:area|base|br|col|embed|hr|img|input|keygen|link|menuitem|meta|param|source|track|wbr)$/i;\nconst spaces = \" \\\\f\\\\n\\\\r\\\\t\";\nconst almostEverything = \"[^\" + spaces + \"\\\\/>\\\"'=]+\";\nconst attrName = \"[ \" + spaces + \"]+(?:use:<!--#-->|\" + almostEverything + ')';\nconst tagName = \"<([A-Za-z$#]+[A-Za-z0-9:_-]*)((?:\";\nconst attrPartials = \"(?:\\\\s*=\\\\s*(?:'[^']*?'|\\\"[^\\\"]*?\\\"|\\\\([^)]*?\\\\)|<[^>]*?>|\" + almostEverything + \"))?)\";\nconst attrSeeker = new RegExp(tagName + attrName + attrPartials + \"+)([ \" + spaces + \"]*/?>)\", \"g\");\nconst findAttributes = new RegExp(\"(\" + attrName + \"\\\\s*=\\\\s*)(<!--#-->|['\\\"(]([\\\\w\\\\s]*<!--#-->[\\\\w\\\\s]*)*['\\\")])\", \"gi\");\nconst selfClosing = new RegExp(tagName + attrName + attrPartials + \"*)([ \" + spaces + \"]*/>)\", \"g\");\nconst marker = \"<!--#-->\";\nconst reservedNameSpaces = new Set([\"class\", \"on\", \"oncapture\", \"style\", \"use\", \"prop\", \"attr\"]);\nfunction attrReplacer($0, $1, $2, $3) {\n  return \"<\" + $1 + $2.replace(findAttributes, replaceAttributes) + $3;\n}\nfunction replaceAttributes($0, $1, $2) {\n  return $1.replace(/<!--#-->/g, \"###\") + ($2[0] === '\"' || $2[0] === \"'\" ? $2.replace(/<!--#-->/g, \"###\") : '\"###\"');\n}\nfunction fullClosing($0, $1, $2) {\n  return VOID_ELEMENTS.test($1) ? $0 : \"<\" + $1 + $2 + \"></\" + $1 + \">\";\n}\nfunction toPropertyName(name) {\n  return name.toLowerCase().replace(/-([a-z])/g, (_, w) => w.toUpperCase());\n}\nfunction parseDirective(name, value, tag, options) {\n  if (name === 'use:###' && value === '###') {\n    const count = options.counter++;\n    options.exprs.push(`typeof exprs[${count}] === \"function\" ? r.use(exprs[${count}], ${tag}, exprs[${options.counter++}]) : (()=>{throw new Error(\"use:### must be a function\")})()`);\n  } else {\n    throw new Error(`Not support syntax ${name} must be use:{function}`);\n  }\n}\nfunction createHTML(r, {\n  delegateEvents = true,\n  functionBuilder = (...args) => new Function(...args)\n} = {}) {\n  let uuid = 1;\n  r.wrapProps = props => {\n    const d = Object.getOwnPropertyDescriptors(props);\n    for (const k in d) {\n      if (typeof d[k].value === \"function\" && !d[k].value.length) r.dynamicProperty(props, k);\n    }\n    return props;\n  };\n  function createTemplate(statics, opt) {\n    let i = 0,\n      markup = \"\";\n    for (; i < statics.length - 1; i++) {\n      markup = markup + statics[i] + \"<!--#-->\";\n    }\n    markup = markup + statics[i];\n    const replaceList = [[selfClosing, fullClosing], [/<(<!--#-->)/g, \"<###\"], [/\\.\\.\\.(<!--#-->)/g, \"###\"], [attrSeeker, attrReplacer], [/>\\n+\\s*/g, \">\"], [/\\n+\\s*</g, \"<\"], [/\\s+</g, \" <\"], [/>\\s+/g, \"> \"]];\n    markup = replaceList.reduce((acc, x) => {\n      return acc.replace(x[0], x[1]);\n    }, markup);\n    const pars = parse(markup);\n    const [html, code] = parseTemplate(pars, opt.funcBuilder),\n      templates = [];\n    for (let i = 0; i < html.length; i++) {\n      templates.push(document.createElement(\"template\"));\n      templates[i].innerHTML = html[i];\n      const nomarkers = templates[i].content.querySelectorAll(\"script,style\");\n      for (let j = 0; j < nomarkers.length; j++) {\n        const d = nomarkers[j].firstChild?.data || \"\";\n        if (d.indexOf(marker) > -1) {\n          const parts = d.split(marker).reduce((memo, p, i) => {\n            i && memo.push(\"\");\n            memo.push(p);\n            return memo;\n          }, []);\n          nomarkers[i].firstChild.replaceWith(...parts);\n        }\n      }\n    }\n    templates[0].create = code;\n    cache.set(statics, templates);\n    return templates;\n  }\n  function parseKeyValue(node, tag, name, value, isSVG, isCE, options) {\n    let expr = value === \"###\" ? `!doNotWrap ? exprs[${options.counter}]() : exprs[${options.counter++}]` : value.split(\"###\").map((v, i) => i ? ` + (typeof exprs[${options.counter}] === \"function\" ? exprs[${options.counter}]() : exprs[${options.counter++}]) + \"${v}\"` : `\"${v}\"`).join(\"\"),\n      parts,\n      namespace;\n    if ((parts = name.split(\":\")) && parts[1] && reservedNameSpaces.has(parts[0])) {\n      name = parts[1];\n      namespace = parts[0];\n    }\n    const isChildProp = r.ChildProperties.has(name);\n    const isProp = r.Properties.has(name);\n    if (name === \"style\") {\n      const prev = `_$v${uuid++}`;\n      options.decl.push(`${prev}={}`);\n      options.exprs.push(`r.style(${tag},${expr},${prev})`);\n    } else if (name === \"classList\") {\n      const prev = `_$v${uuid++}`;\n      options.decl.push(`${prev}={}`);\n      options.exprs.push(`r.classList(${tag},${expr},${prev})`);\n    } else if (namespace !== \"attr\" && (isChildProp || !isSVG && (r.getPropAlias(name, node.name.toUpperCase()) || isProp) || isCE || namespace === \"prop\")) {\n      if (isCE && !isChildProp && !isProp && namespace !== \"prop\") name = toPropertyName(name);\n      options.exprs.push(`${tag}.${r.getPropAlias(name, node.name.toUpperCase()) || name} = ${expr}`);\n    } else {\n      const ns = isSVG && name.indexOf(\":\") > -1 && r.SVGNamespace[name.split(\":\")[0]];\n      if (ns) options.exprs.push(`r.setAttributeNS(${tag},\"${ns}\",\"${name}\",${expr})`);else options.exprs.push(`r.setAttribute(${tag},\"${r.Aliases[name] || name}\",${expr})`);\n    }\n  }\n  function parseAttribute(node, tag, name, value, isSVG, isCE, options) {\n    if (name.slice(0, 2) === \"on\") {\n      if (!name.includes(\":\")) {\n        const lc = name.slice(2).toLowerCase();\n        const delegate = delegateEvents && r.DelegatedEvents.has(lc);\n        options.exprs.push(`r.addEventListener(${tag},\"${lc}\",exprs[${options.counter++}],${delegate})`);\n        delegate && options.delegatedEvents.add(lc);\n      } else {\n        let capture = name.startsWith(\"oncapture:\");\n        options.exprs.push(`${tag}.addEventListener(\"${name.slice(capture ? 10 : 3)}\",exprs[${options.counter++}]${capture ? \",true\" : \"\"})`);\n      }\n    } else if (name === \"ref\") {\n      options.exprs.push(`exprs[${options.counter++}](${tag})`);\n    } else {\n      const childOptions = Object.assign({}, options, {\n          exprs: []\n        }),\n        count = options.counter;\n      parseKeyValue(node, tag, name, value, isSVG, isCE, childOptions);\n      options.decl.push(`_fn${count} = (${value === \"###\" ? \"doNotWrap\" : \"\"}) => {\\n${childOptions.exprs.join(\";\\n\")};\\n}`);\n      if (value === \"###\") {\n        options.exprs.push(`typeof exprs[${count}] === \"function\" ? r.effect(_fn${count}) : _fn${count}(true)`);\n      } else {\n        let check = \"\";\n        for (let i = count; i < childOptions.counter; i++) {\n          i !== count && (check += \" || \");\n          check += `typeof exprs[${i}] === \"function\"`;\n        }\n        options.exprs.push(check + ` ? r.effect(_fn${count}) : _fn${count}()`);\n      }\n      options.counter = childOptions.counter;\n      options.wrap = false;\n    }\n  }\n  function processChildren(node, options) {\n    const childOptions = Object.assign({}, options, {\n      first: true,\n      multi: false,\n      parent: options.path\n    });\n    if (node.children.length > 1) {\n      for (let i = 0; i < node.children.length; i++) {\n        const child = node.children[i];\n        if (child.type === \"comment\" && child.content === \"#\" || child.type === \"tag\" && child.name === \"###\") {\n          childOptions.multi = true;\n          break;\n        }\n      }\n    }\n    let i = 0;\n    while (i < node.children.length) {\n      const child = node.children[i];\n      if (child.name === \"###\") {\n        if (childOptions.multi) {\n          node.children[i] = {\n            type: \"comment\",\n            content: \"#\"\n          };\n          i++;\n        } else node.children.splice(i, 1);\n        processComponent(child, childOptions);\n        continue;\n      }\n      parseNode(child, childOptions);\n      if (!childOptions.multi && child.type === \"comment\" && child.content === \"#\") node.children.splice(i, 1);else i++;\n    }\n    options.counter = childOptions.counter;\n    options.templateId = childOptions.templateId;\n    options.hasCustomElement = options.hasCustomElement || childOptions.hasCustomElement;\n    options.isImportNode = options.isImportNode || childOptions.isImportNode;\n  }\n  function processComponentProps(propGroups) {\n    let result = [];\n    for (const props of propGroups) {\n      if (Array.isArray(props)) {\n        if (!props.length) continue;\n        result.push(`r.wrapProps({${props.join(\",\") || \"\"}})`);\n      } else result.push(props);\n    }\n    return result.length > 1 ? `r.mergeProps(${result.join(\",\")})` : result[0];\n  }\n  function processComponent(node, options) {\n    let props = [];\n    const keys = Object.keys(node.attrs),\n      propGroups = [props],\n      componentIdentifier = options.counter++;\n    for (let i = 0; i < keys.length; i++) {\n      const {\n        type,\n        name,\n        value\n      } = node.attrs[i];\n      if (type === 'attr') {\n        if (name === \"###\") {\n          propGroups.push(`exprs[${options.counter++}]`);\n          propGroups.push(props = []);\n        } else if (value === \"###\") {\n          props.push(`\"${name}\": exprs[${options.counter++}]`);\n        } else props.push(`\"${name}\": \"${value}\"`);\n      } else if (type === 'directive') {\n        const tag = `_$el${uuid++}`;\n        const topDecl = !options.decl.length;\n        options.decl.push(topDecl ? \"\" : `${tag} = ${options.path}.${options.first ? \"firstChild\" : \"nextSibling\"}`);\n        parseDirective(name, value, tag, options);\n      }\n    }\n    if (node.children.length === 1 && node.children[0].type === \"comment\" && node.children[0].content === \"#\") {\n      props.push(`children: () => exprs[${options.counter++}]`);\n    } else if (node.children.length) {\n      const children = {\n          type: \"fragment\",\n          children: node.children\n        },\n        childOptions = Object.assign({}, options, {\n          first: true,\n          decl: [],\n          exprs: [],\n          parent: false\n        });\n      parseNode(children, childOptions);\n      props.push(`children: () => { ${childOptions.exprs.join(\";\\n\")}}`);\n      options.templateId = childOptions.templateId;\n      options.counter = childOptions.counter;\n    }\n    let tag;\n    if (options.multi) {\n      tag = `_$el${uuid++}`;\n      options.decl.push(`${tag} = ${options.path}.${options.first ? \"firstChild\" : \"nextSibling\"}`);\n    }\n    if (options.parent) options.exprs.push(`r.insert(${options.parent}, r.createComponent(exprs[${componentIdentifier}],${processComponentProps(propGroups)})${tag ? `, ${tag}` : \"\"})`);else options.exprs.push(`${options.fragment ? \"\" : \"return \"}r.createComponent(exprs[${componentIdentifier}],${processComponentProps(propGroups)})`);\n    options.path = tag;\n    options.first = false;\n  }\n  function parseNode(node, options) {\n    if (node.type === \"fragment\") {\n      const parts = [];\n      node.children.forEach(child => {\n        if (child.type === \"tag\") {\n          if (child.name === \"###\") {\n            const childOptions = Object.assign({}, options, {\n              first: true,\n              fragment: true,\n              decl: [],\n              exprs: []\n            });\n            processComponent(child, childOptions);\n            parts.push(childOptions.exprs[0]);\n            options.counter = childOptions.counter;\n            options.templateId = childOptions.templateId;\n            return;\n          }\n          options.templateId++;\n          const id = uuid;\n          const childOptions = Object.assign({}, options, {\n            first: true,\n            decl: [],\n            exprs: []\n          });\n          options.templateNodes.push([child]);\n          parseNode(child, childOptions);\n          parts.push(`function() { ${childOptions.decl.join(\",\\n\") + \";\\n\" + childOptions.exprs.join(\";\\n\") + `;\\nreturn _$el${id};\\n`}}()`);\n          options.counter = childOptions.counter;\n          options.templateId = childOptions.templateId;\n        } else if (child.type === \"text\") {\n          parts.push(`\"${child.content}\"`);\n        } else if (child.type === \"comment\") {\n          if (child.content === \"#\") parts.push(`exprs[${options.counter++}]`);else if (child.content) {\n            for (let i = 0; i < child.content.split(\"###\").length - 1; i++) {\n              parts.push(`exprs[${options.counter++}]`);\n            }\n          }\n        }\n      });\n      options.exprs.push(`return [${parts.join(\", \\n\")}]`);\n    } else if (node.type === \"tag\") {\n      const tag = `_$el${uuid++}`;\n      const topDecl = !options.decl.length;\n      const templateId = options.templateId;\n      options.decl.push(topDecl ? \"\" : `${tag} = ${options.path}.${options.first ? \"firstChild\" : \"nextSibling\"}`);\n      const isSVG = r.SVGElements.has(node.name);\n      const isCE = node.name.includes(\"-\") || node.attrs.some(e => e.name === \"is\");\n      options.hasCustomElement = isCE;\n      options.isImportNode = (node.name === 'img' || node.name === 'iframe') && node.attrs.some(e => e.name === \"loading\" && e.value === 'lazy');\n      if (node.attrs.some(e => e.name === \"###\")) {\n        const spreadArgs = [];\n        let current = \"\";\n        const newAttrs = [];\n        for (let i = 0; i < node.attrs.length; i++) {\n          const {\n            type,\n            name,\n            value\n          } = node.attrs[i];\n          if (type === 'attr') {\n            if (value.includes(\"###\")) {\n              let count = options.counter++;\n              current += `${name}: ${name !== \"ref\" ? `typeof exprs[${count}] === \"function\" ? exprs[${count}]() : ` : \"\"}exprs[${count}],`;\n            } else if (name === \"###\") {\n              if (current.length) {\n                spreadArgs.push(`()=>({${current}})`);\n                current = \"\";\n              }\n              spreadArgs.push(`exprs[${options.counter++}]`);\n            } else {\n              newAttrs.push(node.attrs[i]);\n            }\n          } else if (type === 'directive') {\n            parseDirective(name, value, tag, options);\n          }\n        }\n        node.attrs = newAttrs;\n        if (current.length) {\n          spreadArgs.push(`()=>({${current}})`);\n        }\n        options.exprs.push(`r.spread(${tag},${spreadArgs.length === 1 ? `typeof ${spreadArgs[0]} === \"function\" ? r.mergeProps(${spreadArgs[0]}) : ${spreadArgs[0]}` : `r.mergeProps(${spreadArgs.join(\",\")})`},${isSVG},${!!node.children.length})`);\n      } else {\n        for (let i = 0; i < node.attrs.length; i++) {\n          const {\n            type,\n            name,\n            value\n          } = node.attrs[i];\n          if (type === 'directive') {\n            parseDirective(name, value, tag, options);\n            node.attrs.splice(i, 1);\n            i--;\n          } else if (type === \"attr\") {\n            if (value.includes(\"###\")) {\n              node.attrs.splice(i, 1);\n              i--;\n              parseAttribute(node, tag, name, value, isSVG, isCE, options);\n            }\n          }\n        }\n      }\n      options.path = tag;\n      options.first = false;\n      processChildren(node, options);\n      if (topDecl) {\n        options.decl[0] = options.hasCustomElement || options.isImportNode ? `const ${tag} = r.untrack(() => document.importNode(tmpls[${templateId}].content.firstChild, true))` : `const ${tag} = tmpls[${templateId}].content.firstChild.cloneNode(true)`;\n      }\n    } else if (node.type === \"text\") {\n      const tag = `_$el${uuid++}`;\n      options.decl.push(`${tag} = ${options.path}.${options.first ? \"firstChild\" : \"nextSibling\"}`);\n      options.path = tag;\n      options.first = false;\n    } else if (node.type === \"comment\") {\n      const tag = `_$el${uuid++}`;\n      options.decl.push(`${tag} = ${options.path}.${options.first ? \"firstChild\" : \"nextSibling\"}`);\n      if (node.content === \"#\") {\n        if (options.multi) {\n          options.exprs.push(`r.insert(${options.parent}, exprs[${options.counter++}], ${tag})`);\n        } else options.exprs.push(`r.insert(${options.parent}, exprs[${options.counter++}])`);\n      }\n      options.path = tag;\n      options.first = false;\n    }\n  }\n  function parseTemplate(nodes, funcBuilder) {\n    const options = {\n        path: \"\",\n        decl: [],\n        exprs: [],\n        delegatedEvents: new Set(),\n        counter: 0,\n        first: true,\n        multi: false,\n        templateId: 0,\n        templateNodes: []\n      },\n      id = uuid,\n      origNodes = nodes;\n    let toplevel;\n    if (nodes.length > 1) {\n      nodes = [{\n        type: \"fragment\",\n        children: nodes\n      }];\n    }\n    if (nodes[0].name === \"###\") {\n      toplevel = true;\n      processComponent(nodes[0], options);\n    } else parseNode(nodes[0], options);\n    r.delegateEvents(Array.from(options.delegatedEvents));\n    const templateNodes = [origNodes].concat(options.templateNodes);\n    return [templateNodes.map(t => stringify(t)), funcBuilder(\"tmpls\", \"exprs\", \"r\", options.decl.join(\",\\n\") + \";\\n\" + options.exprs.join(\";\\n\") + (toplevel ? \"\" : `;\\nreturn _$el${id};\\n`))];\n  }\n  function html(statics, ...args) {\n    const templates = cache.get(statics) || createTemplate(statics, {\n      funcBuilder: functionBuilder\n    });\n    return templates[0].create(templates, args, r);\n  }\n  return html;\n}\n\nconst html = createHTML({\n  effect,\n  style,\n  insert,\n  untrack,\n  spread,\n  createComponent,\n  delegateEvents,\n  classList,\n  mergeProps,\n  dynamicProperty,\n  setAttribute,\n  setAttributeNS,\n  addEventListener,\n  Aliases,\n  getPropAlias,\n  Properties,\n  ChildProperties,\n  DelegatedEvents,\n  SVGElements,\n  SVGNamespace\n});\n\nexport { html as default };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAM,QAAQ;AACd,IAAM,SAAS;AACf,IAAM,SAAS;AAAA,EACb,MAAM;AAAA,EACN,MAAM;AAAA,EACN,IAAI;AAAA,EACJ,KAAK;AAAA,EACL,OAAO;AAAA,EACP,IAAI;AAAA,EACJ,KAAK;AAAA,EACL,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,UAAU;AAAA,EACV,MAAM;AAAA,EACN,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,KAAK;AACP;AACA,SAAS,SAAS,KAAK;AACrB,QAAM,MAAM;AAAA,IACV,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,OAAO,CAAC;AAAA,IACR,UAAU,CAAC;AAAA,EACb;AACA,QAAM,WAAW,IAAI,MAAM,qBAAqB;AAChD,MAAI,UAAU;AACZ,QAAI,OAAO,SAAS,CAAC;AACrB,QAAI,OAAO,SAAS,CAAC,EAAE,YAAY,CAAC,KAAK,IAAI,OAAO,IAAI,SAAS,CAAC,MAAM,KAAK;AAC3E,UAAI,cAAc;AAAA,IACpB;AACA,QAAI,IAAI,KAAK,WAAW,KAAK,GAAG;AAC9B,YAAM,WAAW,IAAI,QAAQ,KAAK;AAClC,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,aAAa,KAAK,IAAI,MAAM,GAAG,QAAQ,IAAI;AAAA,MACtD;AAAA,IACF;AAAA,EACF;AACA,QAAM,MAAM,IAAI,OAAO,MAAM;AAC7B,aAAW,SAAS,IAAI,SAAS,GAAG,GAAG;AACrC,SAAK,MAAM,CAAC,KAAK,MAAM,CAAC,GAAG,WAAW,MAAM,GAAG;AAC7C,UAAI,MAAM,KAAK;AAAA,QACb,MAAM;AAAA,QACN,MAAM,MAAM,CAAC,KAAK,MAAM,CAAC;AAAA,QACzB,OAAO,MAAM,CAAC,KAAK,MAAM,CAAC,KAAK;AAAA,MACjC,CAAC;AAAA,IACH,OAAO;AACL,UAAI,MAAM,KAAK;AAAA,QACb,MAAM;AAAA,QACN,MAAM,MAAM,CAAC,KAAK,MAAM,CAAC;AAAA,QACzB,OAAO,MAAM,CAAC,KAAK,MAAM,CAAC,KAAK;AAAA,MACjC,CAAC;AAAA,IACH;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,aAAa,MAAMA,OAAM,OAAO;AACvC,QAAM,MAAMA,MAAK,QAAQ,KAAK,KAAK;AACnC,QAAM,UAAUA,MAAK,MAAM,OAAO,QAAQ,KAAK,SAAY,GAAG;AAC9D,MAAI,CAAC,QAAQ,KAAK,OAAO,GAAG;AAC1B,SAAK,KAAK;AAAA,MACR,MAAM;AAAA,MACN;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACA,SAAS,gBAAgB,MAAM,KAAK;AAClC,QAAM,UAAU,IAAI,QAAQ,QAAQ,EAAE,EAAE,QAAQ,OAAO,EAAE;AACzD,MAAI,CAAC,QAAQ,KAAK,OAAO,GAAG;AAC1B,SAAK,KAAK;AAAA,MACR,MAAM;AAAA,MACN;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACA,SAAS,MAAMA,OAAM;AACnB,QAAM,SAAS,CAAC;AAChB,MAAI,UAAU;AACd,MAAI,QAAQ;AACZ,QAAM,MAAM,CAAC;AACb,QAAM,QAAQ,CAAC;AACf,EAAAA,MAAK,QAAQ,OAAO,CAAC,KAAK,UAAU;AAClC,UAAM,SAAS,IAAI,OAAO,CAAC,MAAM;AACjC,UAAM,YAAY,IAAI,MAAM,GAAG,CAAC,MAAM;AACtC,UAAM,QAAQ,QAAQ,IAAI;AAC1B,UAAM,WAAWA,MAAK,OAAO,KAAK;AAClC,QAAI,SAAS;AACb,QAAI,UAAU,CAAC,WAAW;AACxB;AACA,gBAAU,SAAS,GAAG;AACtB,UAAI,CAAC,QAAQ,eAAe,YAAY,aAAa,KAAK;AACxD,qBAAa,QAAQ,UAAUA,OAAM,KAAK;AAAA,MAC5C;AACA,YAAM,QAAQ,OAAO,IAAI;AACzB,UAAI,UAAU,GAAG;AACf,eAAO,KAAK,OAAO;AAAA,MACrB;AACA,eAAS,IAAI,QAAQ,CAAC;AACtB,UAAI,QAAQ;AACV,eAAO,SAAS,KAAK,OAAO;AAAA,MAC9B;AACA,UAAI,KAAK,IAAI;AAAA,IACf;AACA,QAAI,WAAW;AACb,UAAI,QAAQ,GAAG;AACb,wBAAgB,QAAQ,GAAG;AAAA,MAC7B,OAAO;AACL,wBAAgB,IAAI,KAAK,EAAE,UAAU,GAAG;AAAA,MAC1C;AAAA,IACF;AACA,QAAI,aAAa,CAAC,UAAU,QAAQ,aAAa;AAC/C,UAAI,CAAC,WAAW;AACd;AAAA,MACF;AACA,UAAI,aAAa,OAAO,UAAU;AAChC,iBAAS,UAAU,KAAK,SAAS,IAAI,KAAK,EAAE;AAC5C,qBAAa,QAAQA,OAAM,KAAK;AAAA,MAClC;AAAA,IACF;AAAA,EACF,CAAC;AACD,SAAO;AACT;AACA,SAAS,WAAW,OAAO;AACzB,QAAM,OAAO,CAAC;AACd,aAAW,QAAQ,OAAO;AACxB,SAAK,KAAK,KAAK,OAAO,OAAO,KAAK,MAAM,QAAQ,MAAM,QAAQ,IAAI,GAAG;AAAA,EACvE;AACA,MAAI,CAAC,KAAK,QAAQ;AAChB,WAAO;AAAA,EACT;AACA,SAAO,MAAM,KAAK,KAAK,GAAG;AAC5B;AACA,SAAS,YAAY,MAAM,KAAK;AAC9B,UAAQ,IAAI,MAAM;AAAA,IAChB,KAAK;AACH,aAAO,OAAO,IAAI;AAAA,IACpB,KAAK;AACH,cAAQ,MAAM,IAAI,QAAQ,IAAI,QAAQ,WAAW,IAAI,KAAK,IAAI,OAAO,IAAI,cAAc,OAAO;AAC9F,UAAI,IAAI,aAAa;AACnB,eAAO;AAAA,MACT;AACA,aAAO,OAAO,IAAI,SAAS,OAAO,aAAa,EAAE,IAAI,OAAO,IAAI,OAAO;AAAA,IACzE,KAAK;AACH,aAAO,QAAQ,SAAS,IAAI,UAAU;AAAA,EAC1C;AACF;AACA,SAAS,UAAU,KAAK;AACtB,SAAO,IAAI,OAAO,SAAU,OAAO,QAAQ;AACzC,WAAO,QAAQ,YAAY,IAAI,MAAM;AAAA,EACvC,GAAG,EAAE;AACP;AACA,IAAM,QAAQ,oBAAI,IAAI;AACtB,IAAM,gBAAgB;AACtB,IAAM,SAAS;AACf,IAAM,mBAAmB,OAAO,SAAS;AACzC,IAAM,WAAW,OAAO,SAAS,uBAAuB,mBAAmB;AAC3E,IAAM,UAAU;AAChB,IAAM,eAAe,4DAA+D,mBAAmB;AACvG,IAAM,aAAa,IAAI,OAAO,UAAU,WAAW,eAAe,UAAU,SAAS,UAAU,GAAG;AAClG,IAAM,iBAAiB,IAAI,OAAO,MAAM,WAAW,gEAAkE,IAAI;AACzH,IAAM,cAAc,IAAI,OAAO,UAAU,WAAW,eAAe,UAAU,SAAS,SAAS,GAAG;AAClG,IAAM,SAAS;AACf,IAAM,qBAAqB,oBAAI,IAAI,CAAC,SAAS,MAAM,aAAa,SAAS,OAAO,QAAQ,MAAM,CAAC;AAC/F,SAAS,aAAa,IAAI,IAAI,IAAI,IAAI;AACpC,SAAO,MAAM,KAAK,GAAG,QAAQ,gBAAgB,iBAAiB,IAAI;AACpE;AACA,SAAS,kBAAkB,IAAI,IAAI,IAAI;AACrC,SAAO,GAAG,QAAQ,aAAa,KAAK,KAAK,GAAG,CAAC,MAAM,OAAO,GAAG,CAAC,MAAM,MAAM,GAAG,QAAQ,aAAa,KAAK,IAAI;AAC7G;AACA,SAAS,YAAY,IAAI,IAAI,IAAI;AAC/B,SAAO,cAAc,KAAK,EAAE,IAAI,KAAK,MAAM,KAAK,KAAK,QAAQ,KAAK;AACpE;AACA,SAAS,eAAe,MAAM;AAC5B,SAAO,KAAK,YAAY,EAAE,QAAQ,aAAa,CAAC,GAAG,MAAM,EAAE,YAAY,CAAC;AAC1E;AACA,SAAS,eAAe,MAAM,OAAO,KAAK,SAAS;AACjD,MAAI,SAAS,aAAa,UAAU,OAAO;AACzC,UAAM,QAAQ,QAAQ;AACtB,YAAQ,MAAM,KAAK,gBAAgB,KAAK,kCAAkC,KAAK,MAAM,GAAG,WAAW,QAAQ,SAAS,8DAA8D;AAAA,EACpL,OAAO;AACL,UAAM,IAAI,MAAM,sBAAsB,IAAI,yBAAyB;AAAA,EACrE;AACF;AACA,SAAS,WAAW,GAAG;AAAA,EACrB,gBAAAC,kBAAiB;AAAA,EACjB,kBAAkB,IAAI,SAAS,IAAI,SAAS,GAAG,IAAI;AACrD,IAAI,CAAC,GAAG;AACN,MAAI,OAAO;AACX,IAAE,YAAY,WAAS;AACrB,UAAM,IAAI,OAAO,0BAA0B,KAAK;AAChD,eAAW,KAAK,GAAG;AACjB,UAAI,OAAO,EAAE,CAAC,EAAE,UAAU,cAAc,CAAC,EAAE,CAAC,EAAE,MAAM,OAAQ,GAAE,gBAAgB,OAAO,CAAC;AAAA,IACxF;AACA,WAAO;AAAA,EACT;AACA,WAAS,eAAe,SAAS,KAAK;AAzMxC;AA0MI,QAAI,IAAI,GACN,SAAS;AACX,WAAO,IAAI,QAAQ,SAAS,GAAG,KAAK;AAClC,eAAS,SAAS,QAAQ,CAAC,IAAI;AAAA,IACjC;AACA,aAAS,SAAS,QAAQ,CAAC;AAC3B,UAAM,cAAc,CAAC,CAAC,aAAa,WAAW,GAAG,CAAC,gBAAgB,MAAM,GAAG,CAAC,qBAAqB,KAAK,GAAG,CAAC,YAAY,YAAY,GAAG,CAAC,YAAY,GAAG,GAAG,CAAC,YAAY,GAAG,GAAG,CAAC,SAAS,IAAI,GAAG,CAAC,SAAS,IAAI,CAAC;AAC3M,aAAS,YAAY,OAAO,CAAC,KAAK,MAAM;AACtC,aAAO,IAAI,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,IAC/B,GAAG,MAAM;AACT,UAAM,OAAO,MAAM,MAAM;AACzB,UAAM,CAACD,OAAM,IAAI,IAAI,cAAc,MAAM,IAAI,WAAW,GACtD,YAAY,CAAC;AACf,aAASE,KAAI,GAAGA,KAAIF,MAAK,QAAQE,MAAK;AACpC,gBAAU,KAAK,SAAS,cAAc,UAAU,CAAC;AACjD,gBAAUA,EAAC,EAAE,YAAYF,MAAKE,EAAC;AAC/B,YAAM,YAAY,UAAUA,EAAC,EAAE,QAAQ,iBAAiB,cAAc;AACtE,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,cAAM,MAAI,eAAU,CAAC,EAAE,eAAb,mBAAyB,SAAQ;AAC3C,YAAI,EAAE,QAAQ,MAAM,IAAI,IAAI;AAC1B,gBAAM,QAAQ,EAAE,MAAM,MAAM,EAAE,OAAO,CAAC,MAAM,GAAGA,OAAM;AACnD,YAAAA,MAAK,KAAK,KAAK,EAAE;AACjB,iBAAK,KAAK,CAAC;AACX,mBAAO;AAAA,UACT,GAAG,CAAC,CAAC;AACL,oBAAUA,EAAC,EAAE,WAAW,YAAY,GAAG,KAAK;AAAA,QAC9C;AAAA,MACF;AAAA,IACF;AACA,cAAU,CAAC,EAAE,SAAS;AACtB,UAAM,IAAI,SAAS,SAAS;AAC5B,WAAO;AAAA,EACT;AACA,WAAS,cAAc,MAAM,KAAK,MAAM,OAAO,OAAO,MAAM,SAAS;AACnE,QAAI,OAAO,UAAU,QAAQ,sBAAsB,QAAQ,OAAO,eAAe,QAAQ,SAAS,MAAM,MAAM,MAAM,KAAK,EAAE,IAAI,CAAC,GAAG,MAAM,IAAI,oBAAoB,QAAQ,OAAO,4BAA4B,QAAQ,OAAO,eAAe,QAAQ,SAAS,SAAS,CAAC,MAAM,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,GAC1R,OACA;AACF,SAAK,QAAQ,KAAK,MAAM,GAAG,MAAM,MAAM,CAAC,KAAK,mBAAmB,IAAI,MAAM,CAAC,CAAC,GAAG;AAC7E,aAAO,MAAM,CAAC;AACd,kBAAY,MAAM,CAAC;AAAA,IACrB;AACA,UAAM,cAAc,EAAE,gBAAgB,IAAI,IAAI;AAC9C,UAAM,SAAS,EAAE,WAAW,IAAI,IAAI;AACpC,QAAI,SAAS,SAAS;AACpB,YAAM,OAAO,MAAM,MAAM;AACzB,cAAQ,KAAK,KAAK,GAAG,IAAI,KAAK;AAC9B,cAAQ,MAAM,KAAK,WAAW,GAAG,IAAI,IAAI,IAAI,IAAI,GAAG;AAAA,IACtD,WAAW,SAAS,aAAa;AAC/B,YAAM,OAAO,MAAM,MAAM;AACzB,cAAQ,KAAK,KAAK,GAAG,IAAI,KAAK;AAC9B,cAAQ,MAAM,KAAK,eAAe,GAAG,IAAI,IAAI,IAAI,IAAI,GAAG;AAAA,IAC1D,WAAW,cAAc,WAAW,eAAe,CAAC,UAAU,EAAE,aAAa,MAAM,KAAK,KAAK,YAAY,CAAC,KAAK,WAAW,QAAQ,cAAc,SAAS;AACvJ,UAAI,QAAQ,CAAC,eAAe,CAAC,UAAU,cAAc,OAAQ,QAAO,eAAe,IAAI;AACvF,cAAQ,MAAM,KAAK,GAAG,GAAG,IAAI,EAAE,aAAa,MAAM,KAAK,KAAK,YAAY,CAAC,KAAK,IAAI,MAAM,IAAI,EAAE;AAAA,IAChG,OAAO;AACL,YAAM,KAAK,SAAS,KAAK,QAAQ,GAAG,IAAI,MAAM,EAAE,aAAa,KAAK,MAAM,GAAG,EAAE,CAAC,CAAC;AAC/E,UAAI,GAAI,SAAQ,MAAM,KAAK,oBAAoB,GAAG,KAAK,EAAE,MAAM,IAAI,KAAK,IAAI,GAAG;AAAA,UAAO,SAAQ,MAAM,KAAK,kBAAkB,GAAG,KAAK,EAAE,QAAQ,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG;AAAA,IACxK;AAAA,EACF;AACA,WAAS,eAAe,MAAM,KAAK,MAAM,OAAO,OAAO,MAAM,SAAS;AACpE,QAAI,KAAK,MAAM,GAAG,CAAC,MAAM,MAAM;AAC7B,UAAI,CAAC,KAAK,SAAS,GAAG,GAAG;AACvB,cAAM,KAAK,KAAK,MAAM,CAAC,EAAE,YAAY;AACrC,cAAM,WAAWD,mBAAkB,EAAE,gBAAgB,IAAI,EAAE;AAC3D,gBAAQ,MAAM,KAAK,sBAAsB,GAAG,KAAK,EAAE,WAAW,QAAQ,SAAS,KAAK,QAAQ,GAAG;AAC/F,oBAAY,QAAQ,gBAAgB,IAAI,EAAE;AAAA,MAC5C,OAAO;AACL,YAAI,UAAU,KAAK,WAAW,YAAY;AAC1C,gBAAQ,MAAM,KAAK,GAAG,GAAG,sBAAsB,KAAK,MAAM,UAAU,KAAK,CAAC,CAAC,WAAW,QAAQ,SAAS,IAAI,UAAU,UAAU,EAAE,GAAG;AAAA,MACtI;AAAA,IACF,WAAW,SAAS,OAAO;AACzB,cAAQ,MAAM,KAAK,SAAS,QAAQ,SAAS,KAAK,GAAG,GAAG;AAAA,IAC1D,OAAO;AACL,YAAM,eAAe,OAAO,OAAO,CAAC,GAAG,SAAS;AAAA,QAC5C,OAAO,CAAC;AAAA,MACV,CAAC,GACD,QAAQ,QAAQ;AAClB,oBAAc,MAAM,KAAK,MAAM,OAAO,OAAO,MAAM,YAAY;AAC/D,cAAQ,KAAK,KAAK,MAAM,KAAK,OAAO,UAAU,QAAQ,cAAc,EAAE;AAAA,EAAW,aAAa,MAAM,KAAK,KAAK,CAAC;AAAA,EAAM;AACrH,UAAI,UAAU,OAAO;AACnB,gBAAQ,MAAM,KAAK,gBAAgB,KAAK,kCAAkC,KAAK,UAAU,KAAK,QAAQ;AAAA,MACxG,OAAO;AACL,YAAI,QAAQ;AACZ,iBAAS,IAAI,OAAO,IAAI,aAAa,SAAS,KAAK;AACjD,gBAAM,UAAU,SAAS;AACzB,mBAAS,gBAAgB,CAAC;AAAA,QAC5B;AACA,gBAAQ,MAAM,KAAK,QAAQ,kBAAkB,KAAK,UAAU,KAAK,IAAI;AAAA,MACvE;AACA,cAAQ,UAAU,aAAa;AAC/B,cAAQ,OAAO;AAAA,IACjB;AAAA,EACF;AACA,WAAS,gBAAgB,MAAM,SAAS;AACtC,UAAM,eAAe,OAAO,OAAO,CAAC,GAAG,SAAS;AAAA,MAC9C,OAAO;AAAA,MACP,OAAO;AAAA,MACP,QAAQ,QAAQ;AAAA,IAClB,CAAC;AACD,QAAI,KAAK,SAAS,SAAS,GAAG;AAC5B,eAASC,KAAI,GAAGA,KAAI,KAAK,SAAS,QAAQA,MAAK;AAC7C,cAAM,QAAQ,KAAK,SAASA,EAAC;AAC7B,YAAI,MAAM,SAAS,aAAa,MAAM,YAAY,OAAO,MAAM,SAAS,SAAS,MAAM,SAAS,OAAO;AACrG,uBAAa,QAAQ;AACrB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,QAAI,IAAI;AACR,WAAO,IAAI,KAAK,SAAS,QAAQ;AAC/B,YAAM,QAAQ,KAAK,SAAS,CAAC;AAC7B,UAAI,MAAM,SAAS,OAAO;AACxB,YAAI,aAAa,OAAO;AACtB,eAAK,SAAS,CAAC,IAAI;AAAA,YACjB,MAAM;AAAA,YACN,SAAS;AAAA,UACX;AACA;AAAA,QACF,MAAO,MAAK,SAAS,OAAO,GAAG,CAAC;AAChC,yBAAiB,OAAO,YAAY;AACpC;AAAA,MACF;AACA,gBAAU,OAAO,YAAY;AAC7B,UAAI,CAAC,aAAa,SAAS,MAAM,SAAS,aAAa,MAAM,YAAY,IAAK,MAAK,SAAS,OAAO,GAAG,CAAC;AAAA,UAAO;AAAA,IAChH;AACA,YAAQ,UAAU,aAAa;AAC/B,YAAQ,aAAa,aAAa;AAClC,YAAQ,mBAAmB,QAAQ,oBAAoB,aAAa;AACpE,YAAQ,eAAe,QAAQ,gBAAgB,aAAa;AAAA,EAC9D;AACA,WAAS,sBAAsB,YAAY;AACzC,QAAI,SAAS,CAAC;AACd,eAAW,SAAS,YAAY;AAC9B,UAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,YAAI,CAAC,MAAM,OAAQ;AACnB,eAAO,KAAK,gBAAgB,MAAM,KAAK,GAAG,KAAK,EAAE,IAAI;AAAA,MACvD,MAAO,QAAO,KAAK,KAAK;AAAA,IAC1B;AACA,WAAO,OAAO,SAAS,IAAI,gBAAgB,OAAO,KAAK,GAAG,CAAC,MAAM,OAAO,CAAC;AAAA,EAC3E;AACA,WAAS,iBAAiB,MAAM,SAAS;AACvC,QAAI,QAAQ,CAAC;AACb,UAAM,OAAO,OAAO,KAAK,KAAK,KAAK,GACjC,aAAa,CAAC,KAAK,GACnB,sBAAsB,QAAQ;AAChC,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI,KAAK,MAAM,CAAC;AAChB,UAAI,SAAS,QAAQ;AACnB,YAAI,SAAS,OAAO;AAClB,qBAAW,KAAK,SAAS,QAAQ,SAAS,GAAG;AAC7C,qBAAW,KAAK,QAAQ,CAAC,CAAC;AAAA,QAC5B,WAAW,UAAU,OAAO;AAC1B,gBAAM,KAAK,IAAI,IAAI,YAAY,QAAQ,SAAS,GAAG;AAAA,QACrD,MAAO,OAAM,KAAK,IAAI,IAAI,OAAO,KAAK,GAAG;AAAA,MAC3C,WAAW,SAAS,aAAa;AAC/B,cAAMC,OAAM,OAAO,MAAM;AACzB,cAAM,UAAU,CAAC,QAAQ,KAAK;AAC9B,gBAAQ,KAAK,KAAK,UAAU,KAAK,GAAGA,IAAG,MAAM,QAAQ,IAAI,IAAI,QAAQ,QAAQ,eAAe,aAAa,EAAE;AAC3G,uBAAe,MAAM,OAAOA,MAAK,OAAO;AAAA,MAC1C;AAAA,IACF;AACA,QAAI,KAAK,SAAS,WAAW,KAAK,KAAK,SAAS,CAAC,EAAE,SAAS,aAAa,KAAK,SAAS,CAAC,EAAE,YAAY,KAAK;AACzG,YAAM,KAAK,yBAAyB,QAAQ,SAAS,GAAG;AAAA,IAC1D,WAAW,KAAK,SAAS,QAAQ;AAC/B,YAAM,WAAW;AAAA,QACb,MAAM;AAAA,QACN,UAAU,KAAK;AAAA,MACjB,GACA,eAAe,OAAO,OAAO,CAAC,GAAG,SAAS;AAAA,QACxC,OAAO;AAAA,QACP,MAAM,CAAC;AAAA,QACP,OAAO,CAAC;AAAA,QACR,QAAQ;AAAA,MACV,CAAC;AACH,gBAAU,UAAU,YAAY;AAChC,YAAM,KAAK,qBAAqB,aAAa,MAAM,KAAK,KAAK,CAAC,GAAG;AACjE,cAAQ,aAAa,aAAa;AAClC,cAAQ,UAAU,aAAa;AAAA,IACjC;AACA,QAAI;AACJ,QAAI,QAAQ,OAAO;AACjB,YAAM,OAAO,MAAM;AACnB,cAAQ,KAAK,KAAK,GAAG,GAAG,MAAM,QAAQ,IAAI,IAAI,QAAQ,QAAQ,eAAe,aAAa,EAAE;AAAA,IAC9F;AACA,QAAI,QAAQ,OAAQ,SAAQ,MAAM,KAAK,YAAY,QAAQ,MAAM,6BAA6B,mBAAmB,KAAK,sBAAsB,UAAU,CAAC,IAAI,MAAM,KAAK,GAAG,KAAK,EAAE,GAAG;AAAA,QAAO,SAAQ,MAAM,KAAK,GAAG,QAAQ,WAAW,KAAK,SAAS,2BAA2B,mBAAmB,KAAK,sBAAsB,UAAU,CAAC,GAAG;AACxU,YAAQ,OAAO;AACf,YAAQ,QAAQ;AAAA,EAClB;AACA,WAAS,UAAU,MAAM,SAAS;AAChC,QAAI,KAAK,SAAS,YAAY;AAC5B,YAAM,QAAQ,CAAC;AACf,WAAK,SAAS,QAAQ,WAAS;AAC7B,YAAI,MAAM,SAAS,OAAO;AACxB,cAAI,MAAM,SAAS,OAAO;AACxB,kBAAMC,gBAAe,OAAO,OAAO,CAAC,GAAG,SAAS;AAAA,cAC9C,OAAO;AAAA,cACP,UAAU;AAAA,cACV,MAAM,CAAC;AAAA,cACP,OAAO,CAAC;AAAA,YACV,CAAC;AACD,6BAAiB,OAAOA,aAAY;AACpC,kBAAM,KAAKA,cAAa,MAAM,CAAC,CAAC;AAChC,oBAAQ,UAAUA,cAAa;AAC/B,oBAAQ,aAAaA,cAAa;AAClC;AAAA,UACF;AACA,kBAAQ;AACR,gBAAM,KAAK;AACX,gBAAM,eAAe,OAAO,OAAO,CAAC,GAAG,SAAS;AAAA,YAC9C,OAAO;AAAA,YACP,MAAM,CAAC;AAAA,YACP,OAAO,CAAC;AAAA,UACV,CAAC;AACD,kBAAQ,cAAc,KAAK,CAAC,KAAK,CAAC;AAClC,oBAAU,OAAO,YAAY;AAC7B,gBAAM,KAAK,gBAAgB,aAAa,KAAK,KAAK,KAAK,IAAI,QAAQ,aAAa,MAAM,KAAK,KAAK,IAAI;AAAA,aAAiB,EAAE;AAAA,CAAK,KAAK;AACjI,kBAAQ,UAAU,aAAa;AAC/B,kBAAQ,aAAa,aAAa;AAAA,QACpC,WAAW,MAAM,SAAS,QAAQ;AAChC,gBAAM,KAAK,IAAI,MAAM,OAAO,GAAG;AAAA,QACjC,WAAW,MAAM,SAAS,WAAW;AACnC,cAAI,MAAM,YAAY,IAAK,OAAM,KAAK,SAAS,QAAQ,SAAS,GAAG;AAAA,mBAAW,MAAM,SAAS;AAC3F,qBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,MAAM,KAAK,EAAE,SAAS,GAAG,KAAK;AAC9D,oBAAM,KAAK,SAAS,QAAQ,SAAS,GAAG;AAAA,YAC1C;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AACD,cAAQ,MAAM,KAAK,WAAW,MAAM,KAAK,MAAM,CAAC,GAAG;AAAA,IACrD,WAAW,KAAK,SAAS,OAAO;AAC9B,YAAM,MAAM,OAAO,MAAM;AACzB,YAAM,UAAU,CAAC,QAAQ,KAAK;AAC9B,YAAM,aAAa,QAAQ;AAC3B,cAAQ,KAAK,KAAK,UAAU,KAAK,GAAG,GAAG,MAAM,QAAQ,IAAI,IAAI,QAAQ,QAAQ,eAAe,aAAa,EAAE;AAC3G,YAAM,QAAQ,EAAE,YAAY,IAAI,KAAK,IAAI;AACzC,YAAM,OAAO,KAAK,KAAK,SAAS,GAAG,KAAK,KAAK,MAAM,KAAK,OAAK,EAAE,SAAS,IAAI;AAC5E,cAAQ,mBAAmB;AAC3B,cAAQ,gBAAgB,KAAK,SAAS,SAAS,KAAK,SAAS,aAAa,KAAK,MAAM,KAAK,OAAK,EAAE,SAAS,aAAa,EAAE,UAAU,MAAM;AACzI,UAAI,KAAK,MAAM,KAAK,OAAK,EAAE,SAAS,KAAK,GAAG;AAC1C,cAAM,aAAa,CAAC;AACpB,YAAI,UAAU;AACd,cAAM,WAAW,CAAC;AAClB,iBAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAK;AAC1C,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,YACA;AAAA,UACF,IAAI,KAAK,MAAM,CAAC;AAChB,cAAI,SAAS,QAAQ;AACnB,gBAAI,MAAM,SAAS,KAAK,GAAG;AACzB,kBAAI,QAAQ,QAAQ;AACpB,yBAAW,GAAG,IAAI,KAAK,SAAS,QAAQ,gBAAgB,KAAK,4BAA4B,KAAK,WAAW,EAAE,SAAS,KAAK;AAAA,YAC3H,WAAW,SAAS,OAAO;AACzB,kBAAI,QAAQ,QAAQ;AAClB,2BAAW,KAAK,SAAS,OAAO,IAAI;AACpC,0BAAU;AAAA,cACZ;AACA,yBAAW,KAAK,SAAS,QAAQ,SAAS,GAAG;AAAA,YAC/C,OAAO;AACL,uBAAS,KAAK,KAAK,MAAM,CAAC,CAAC;AAAA,YAC7B;AAAA,UACF,WAAW,SAAS,aAAa;AAC/B,2BAAe,MAAM,OAAO,KAAK,OAAO;AAAA,UAC1C;AAAA,QACF;AACA,aAAK,QAAQ;AACb,YAAI,QAAQ,QAAQ;AAClB,qBAAW,KAAK,SAAS,OAAO,IAAI;AAAA,QACtC;AACA,gBAAQ,MAAM,KAAK,YAAY,GAAG,IAAI,WAAW,WAAW,IAAI,UAAU,WAAW,CAAC,CAAC,kCAAkC,WAAW,CAAC,CAAC,OAAO,WAAW,CAAC,CAAC,KAAK,gBAAgB,WAAW,KAAK,GAAG,CAAC,GAAG,IAAI,KAAK,IAAI,CAAC,CAAC,KAAK,SAAS,MAAM,GAAG;AAAA,MAC9O,OAAO;AACL,iBAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAK;AAC1C,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,YACA;AAAA,UACF,IAAI,KAAK,MAAM,CAAC;AAChB,cAAI,SAAS,aAAa;AACxB,2BAAe,MAAM,OAAO,KAAK,OAAO;AACxC,iBAAK,MAAM,OAAO,GAAG,CAAC;AACtB;AAAA,UACF,WAAW,SAAS,QAAQ;AAC1B,gBAAI,MAAM,SAAS,KAAK,GAAG;AACzB,mBAAK,MAAM,OAAO,GAAG,CAAC;AACtB;AACA,6BAAe,MAAM,KAAK,MAAM,OAAO,OAAO,MAAM,OAAO;AAAA,YAC7D;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,cAAQ,OAAO;AACf,cAAQ,QAAQ;AAChB,sBAAgB,MAAM,OAAO;AAC7B,UAAI,SAAS;AACX,gBAAQ,KAAK,CAAC,IAAI,QAAQ,oBAAoB,QAAQ,eAAe,SAAS,GAAG,gDAAgD,UAAU,iCAAiC,SAAS,GAAG,YAAY,UAAU;AAAA,MAChN;AAAA,IACF,WAAW,KAAK,SAAS,QAAQ;AAC/B,YAAM,MAAM,OAAO,MAAM;AACzB,cAAQ,KAAK,KAAK,GAAG,GAAG,MAAM,QAAQ,IAAI,IAAI,QAAQ,QAAQ,eAAe,aAAa,EAAE;AAC5F,cAAQ,OAAO;AACf,cAAQ,QAAQ;AAAA,IAClB,WAAW,KAAK,SAAS,WAAW;AAClC,YAAM,MAAM,OAAO,MAAM;AACzB,cAAQ,KAAK,KAAK,GAAG,GAAG,MAAM,QAAQ,IAAI,IAAI,QAAQ,QAAQ,eAAe,aAAa,EAAE;AAC5F,UAAI,KAAK,YAAY,KAAK;AACxB,YAAI,QAAQ,OAAO;AACjB,kBAAQ,MAAM,KAAK,YAAY,QAAQ,MAAM,WAAW,QAAQ,SAAS,MAAM,GAAG,GAAG;AAAA,QACvF,MAAO,SAAQ,MAAM,KAAK,YAAY,QAAQ,MAAM,WAAW,QAAQ,SAAS,IAAI;AAAA,MACtF;AACA,cAAQ,OAAO;AACf,cAAQ,QAAQ;AAAA,IAClB;AAAA,EACF;AACA,WAAS,cAAc,OAAO,aAAa;AACzC,UAAM,UAAU;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,MACP,OAAO,CAAC;AAAA,MACR,iBAAiB,oBAAI,IAAI;AAAA,MACzB,SAAS;AAAA,MACT,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,eAAe,CAAC;AAAA,IAClB,GACA,KAAK,MACL,YAAY;AACd,QAAI;AACJ,QAAI,MAAM,SAAS,GAAG;AACpB,cAAQ,CAAC;AAAA,QACP,MAAM;AAAA,QACN,UAAU;AAAA,MACZ,CAAC;AAAA,IACH;AACA,QAAI,MAAM,CAAC,EAAE,SAAS,OAAO;AAC3B,iBAAW;AACX,uBAAiB,MAAM,CAAC,GAAG,OAAO;AAAA,IACpC,MAAO,WAAU,MAAM,CAAC,GAAG,OAAO;AAClC,MAAE,eAAe,MAAM,KAAK,QAAQ,eAAe,CAAC;AACpD,UAAM,gBAAgB,CAAC,SAAS,EAAE,OAAO,QAAQ,aAAa;AAC9D,WAAO,CAAC,cAAc,IAAI,OAAK,UAAU,CAAC,CAAC,GAAG,YAAY,SAAS,SAAS,KAAK,QAAQ,KAAK,KAAK,KAAK,IAAI,QAAQ,QAAQ,MAAM,KAAK,KAAK,KAAK,WAAW,KAAK;AAAA,aAAiB,EAAE;AAAA,EAAM,CAAC;AAAA,EAC7L;AACA,WAASJ,MAAK,YAAY,MAAM;AAC9B,UAAM,YAAY,MAAM,IAAI,OAAO,KAAK,eAAe,SAAS;AAAA,MAC9D,aAAa;AAAA,IACf,CAAC;AACD,WAAO,UAAU,CAAC,EAAE,OAAO,WAAW,MAAM,CAAC;AAAA,EAC/C;AACA,SAAOA;AACT;AAEA,IAAM,OAAO,WAAW;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;", "names": ["html", "delegateEvents", "i", "tag", "childOptions"]}